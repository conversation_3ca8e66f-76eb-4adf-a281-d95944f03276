// app/context/AuthContext.tsx
import React, { createContext, useContext, useEffect, useState } from 'react';

import {
  clearTokens,
  getAccessToken,
  isUserLoggedIn,
} from '@/lib/auth/tokenManager';
import { clearUser, getUserObject } from '@/lib/user/user';
import type { StoredUser } from '@/types/auth';

type AuthContextType = {
  user: StoredUser | null;
  accessToken: string | null;
  isLoading: boolean;
  isLoggedIn: boolean;
  logout: () => Promise<void>;
  login: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<StoredUser | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const loadTokens = async () => {
      try {
        const isLoggedIn = await isUserLoggedIn();

        if (isLoggedIn) {
          const token = await getAccessToken();
          const savedUser = await getUserObject();

          setAccessToken(token);
          setUser(savedUser);
        } else {
          setAccessToken(null);
          setUser(null);
        }
      } catch (error) {
        console.error('Error loading authentication state:', error);
        setAccessToken(null);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    loadTokens();
  }, []);

  const login = async () => {
    try {
      const isLoggedIn = await isUserLoggedIn();

      if (isLoggedIn) {
        const token = await getAccessToken();
        const savedUser = await getUserObject();

        setAccessToken(token);
        setUser(savedUser);
      } else {
        setAccessToken(null);
        setUser(null);
      }
    } catch (error) {
      console.error('Error loading authentication state:', error);
      setAccessToken(null);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    await Promise.all([clearTokens(), clearUser()]);
    setUser(null);
    setAccessToken(null);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        accessToken,
        isLoading,
        isLoggedIn: !!user && !!accessToken,
        logout,
        login,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const ctx = useContext(AuthContext);
  if (!ctx) throw new Error('useAuth must be used within an AuthProvider');
  return ctx;
};
