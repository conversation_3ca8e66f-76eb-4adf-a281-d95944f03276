import { Platform, StyleProp, TextStyle, ViewStyle } from 'react-native';

import { COLOURS } from './colours';

type InputStyles = {
  checkbox?: StyleProp<ViewStyle>;
  checkboxContainer?: StyleProp<ViewStyle>;
  checkboxLabel?: StyleProp<TextStyle>;
  color?: StyleProp<TextStyle>;
  fontFamily?: StyleProp<TextStyle>;
  fontSize?: StyleProp<TextStyle>;
  textInput?: StyleProp<TextStyle>;
  textInputView?: StyleProp<ViewStyle>;
};

export const INPUT_STYLES: InputStyles = {
  textInput: {
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 16,
    padding: 16,
    marginVertical: 10,
  },
  textInputView: {
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    paddingVertical: Platform.OS == 'android' ? 8 : undefined,
    marginVertical: 10,
  },
  checkbox: {
    borderRadius: 5,
    borderColor: COLOURS.primary,
    borderWidth: 2,
    marginVertical: 10,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    marginVertical: 5,
    gap: 10,
  },
  checkboxLabel: {
    fontFamily: 'Comfortaa-Regular',
    fontSize: 14,
    color: COLOURS.secondary,
  },
};
