/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#0a7ea4';
const tintColorDark = '#fff';

export const COLOURS = {
  background: 'hsl(0, 0%, 100%)',
  grey: 'hsl(0, 0%, 98%)',
  greyMedium: 'hsl(60, 0%, 93%)',
  greyShade: 'hsl(0, 0%, 90%)',
  greyDark: 'hsl(60, 0%, 70%)',
  greyContrast: 'hsl(60, 0%, 30%)',
  primary: 'hsl(186, 53%, 30%)',
  primaryTint: 'hsl(186, 53%, 40%)',
  secondary: 'hsl(257, 49%, 11%)',
  secondaryTint: 'hsl(258, 6%, 56%)',
  secondaryShade: 'hsl(256, 46%, 25%)',
  tertiary: 'hsl(36, 70%, 85%)',
  tertiaryTint: 'hsl(34, 70%, 95%)',
  tertiaryShade: 'hsl(36, 68%, 60%)',
  text: 'hsl(202, 24%, 9%) ',
  white: 'hsl(0, 0%, 100%)',
  upgardeBg: 'hsl(255, 7%, 89%)',
  modalBackground: 'hsla(0, 0%, 0%, 0.2)',
  black: 'hsl(0, 0%, 0%)',
  backgroundModal: 'hsla(0, 0%, 0%, 0.3)',
  orange: 'hsl(39, 81%, 87%)',
  youText: 'hsl(250, 6%, 57%)',
  textBlack: 'hsl(257, 47%, 11%)',
  borderColor: 'hsl(0, 0%, 93%)',
  placeholderText: 'hsl(0, 0%, 69%)',
  whiteColor: 'hsl(0, 0%, 100%)',
  grayIcon: 'hsl(0, 0%, 70%)',
  errorText: 'hsl(0, 59%, 51%)',
  greenText: 'hsl(191, 50%, 30%)',
  lightGreen: 'hsl(186, 56%, 92%)',
  greyLight: 'hsl(0, 0%, 98%)',
  diableText: 'hsl(0, 2%, 71%)',
  disableColor: 'hsl(0, 0%, 98%)',
  infoBoxColor: 'hsla(186, 56%, 92%, 0.4)',
  uppgradeBoxColor: 'hsla(250, 6%, 57%, 0.25)',
  upgradeText: 'hsl(256, 48%, 25%)',
  qrBackground: 'hsl(257, 47%, 11%)',
  yellowFrame: 'hsl(36, 68%, 60%)',
  loaderBackground: 'hsla(0, 0%, 100%, 0.95)',
  lightPrimary: 'hsl(185, 52%, 96%)',
  mediumPrimary: 'hsl(187, 53%, 90%)',
  darkGrayText: 'hsl(189, 4%, 67%)',
  lightGray: 'hsl(0, 0%, 95%)',
  lightRed: 'hsl(0, 73%, 90%)',
  lightYellow: 'hsl(36, 68%, 85%)',
  redText: 'hsl(0, 60%, 51%)',
  goldText: 'hsl(36, 57%, 47%)',
  dayText: 'hsl(188, 53%, 20%)',
  // Light and Dark mode colours below can be ignored for now.//
  light: {
    tint: tintColorLight,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,
  },
  dark: {
    text: '#ECEDEE',
    background: '#151718',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
  },
};
