export interface LoginUser {
  sub: number;
  email: string;
  isAdmin: boolean;
  isVet: number;
  title: string;
  firstName: string;
  lastName: string;
}

export interface StoredUser {
  id: string;
  email: string;
  isVet?: boolean;
  title?: string;
  firstName?: string;
  lastName?: string;
}

export interface LoginResponse {
  accessToken: string;
  user: LoginUser;
}

export interface JWTPayload {
  sub: string;
  email: string;
  isAdmin: boolean;
  isVet: number;
  title: string;
  firstName: string;
  lastName: string;
  iat?: number;
  exp?: number;
  [key: string]: any;
} 