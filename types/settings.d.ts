import type { <PERSON><PERSON><PERSON>, ViewStyle } from 'react-native';
import type { ComponentType } from 'react';

export type SettingsMenuItem = {
  key: string;
  icon: ComponentType<any>;
  label: string;
  description?: string;
  badge?: {
    icon: ComponentType<any>;
    label: string;
  };
  rightText?: string;
  rightIcon?: ComponentType<any>;
  onPress?: () => void;
};

export type SettingsItemProps = {
  item: SettingsMenuItem;
  style?: StyleProp<ViewStyle>;
  onPress?: () => void;
}; 