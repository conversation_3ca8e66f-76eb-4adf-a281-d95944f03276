import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLOURS.white,
    },
    headerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 0,
        paddingLeft: 18,
        paddingRight: 24,
        paddingTop: 10,
    },
    backBtn: {
        padding: 4,
    },
    languageRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    languageText: {
        color: COLOURS.greenText,
        fontSize: 16,
        fontFamily: 'Comfortaa-Bold',
        fontWeight: '700',
    },
    languageTextButton: {
        marginLeft: 4,
    },
    progressBarBg: {
        flexDirection: 'row',
        height: 7,
        backgroundColor: COLOURS.lightGreen,
        borderRadius: 4,
        marginHorizontal: 24,
        marginTop: 20,
        overflow: 'hidden',
    },
    progressBarFill: {
        backgroundColor: COLOURS.primary,
        borderRadius: 4,
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: COLOURS.backgroundModal,
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 24,
        alignItems: 'center',
        minWidth: 260,
        elevation: 5,
        shadowColor: COLOURS.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
    },
    modalIcon: {
        marginBottom: 16,
    },
    modalErrorText: {
        color: COLOURS.textBlack,
        fontSize: 16,
        marginBottom: 20,
        textAlign: 'center',
    },
    modalButton: {
        backgroundColor: COLOURS.primary,
        borderRadius: 8,
        paddingVertical: 10,
        paddingHorizontal: 32,
        alignItems: 'center',
    },
    modalButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 16,
    },
    errorContainer: {
        paddingHorizontal: 24,
    },
});

export default styles; 