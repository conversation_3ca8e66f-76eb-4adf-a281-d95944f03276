import { Ionicons } from '@expo/vector-icons';
import Checkbox from 'expo-checkbox';
import { Formik } from 'formik';
import {
  Platform,
  ScrollView,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';

import { CallIcon, LocationIcon } from '@/components/icons';
import ErrorIcon from '@/components/icons/Error';
import ShowEye from '@/components/icons/ShowEye';
import TextTypes from '@/components/text-types';
import Button from '@/components/ui/button';
import { COLOURS } from '@/constants/colours';
import { INPUT_STYLES } from '@/constants/formInputs';

import { vetProfessionalValidationSchema, vetRoles } from './services';
import styles from './styles';

export default function StepVetProfessionalAccount({
  onContinue,
  affiliatedPractice,
  initialValues,
  onEditClick
}: {
  onContinue: (data: any) => void;
  affiliatedPractice: { name: string; address: string; phone: string };
  initialValues: any;
  onEditClick: () => void;
}) {
  const {
    wrapper,
    formGroup,
    title,
    subtitle,
    practiceBox,
    practiceName,
    editButtonText,
    autoFilled,
    infoBox,
    infoIcon,
    infoText,
    placeholderStyle,
    pv12,
    flex1,
    affiliateInfo,
    checkbox,
    checkboxRow,
    errorRow,
    btnText
  } = styles;

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={vetProfessionalValidationSchema}
      validateOnMount
      onSubmit={onContinue}
    >
      {({
        handleChange,
        handleBlur,
        handleSubmit,
        values,
        errors,
        touched,
        setFieldValue,
        isValid,
        submitCount,
      }) => (
        <ScrollView
          contentContainerStyle={wrapper}
          keyboardShouldPersistTaps='handled'
          showsVerticalScrollIndicator={false}
        >
          <TextTypes type='h1' color={COLOURS.primary} customStyle={title}>
            Create your account
          </TextTypes>
          <TextTypes
            type='body2'
            color={COLOURS.secondaryTint}
            customStyle={subtitle}
          >
            Just a few details to get you started. You can complete your full
            profile later.
          </TextTypes>
          <View style={formGroup}>
            <TextTypes type='h5' color={COLOURS.textBlack}>
              First name
              <TextTypes type='h5' color={COLOURS.primary}>
                *
              </TextTypes>
            </TextTypes>
            <TextInput
              style={INPUT_STYLES.textInput}
              placeholder='Enter your first name'
              value={values.firstName}
              onChangeText={handleChange('firstName')}
              onBlur={handleBlur('firstName')}
              placeholderTextColor={COLOURS.greyDark}
              autoCapitalize='words'
            />
            {(touched.firstName || submitCount > 0) && errors.firstName && (
              <View style={errorRow}>
                <ErrorIcon style={{ marginRight: 6 }} />
                <TextTypes type='errorText' color={COLOURS.errorText}>
                  {typeof errors.firstName === 'string' ? errors.firstName : ''}
                </TextTypes>
              </View>
            )}
          </View>
          <View style={formGroup}>
            <TextTypes type='h5' color={COLOURS.textBlack}>
              Last name
              <TextTypes type='h5' color={COLOURS.primary}>
                *
              </TextTypes>
            </TextTypes>
            <TextInput
              style={INPUT_STYLES.textInput}
              placeholder='Enter your last name'
              value={values.lastName}
              onChangeText={handleChange('lastName')}
              onBlur={handleBlur('lastName')}
              placeholderTextColor={COLOURS.greyDark}
              autoCapitalize='words'
            />
            {(touched.lastName || submitCount > 0) && errors.lastName && (
              <View style={errorRow}>
                <ErrorIcon style={{ marginRight: 6 }} />
                <TextTypes type='errorText' color={COLOURS.errorText}>
                  {typeof errors.lastName === 'string' ? errors.lastName : ''}
                </TextTypes>
              </View>
            )}
          </View>
          <View style={formGroup}>
            <TextTypes type='h5' color={COLOURS.textBlack}>
              Email address
              <TextTypes type='h5' color={COLOURS.primary}>
                *
              </TextTypes>
            </TextTypes>
            <TextInput
              style={INPUT_STYLES.textInput}
              placeholder='Enter email'
              value={values.email}
              onChangeText={handleChange('email')}
              onBlur={handleBlur('email')}
              placeholderTextColor={COLOURS.greyDark}
              keyboardType='email-address'
              autoCapitalize='none'
            />
            {(touched.email || submitCount > 0) && errors.email && (
              <View style={errorRow}>
                <ErrorIcon style={{ marginRight: 6 }} />
                <TextTypes type='errorText' color={COLOURS.errorText}>
                  {typeof errors.email === 'string' ? errors.email : ''}
                </TextTypes>
              </View>
            )}
          </View>
          <View style={formGroup}>
            <TextTypes type='h5' color={COLOURS.textBlack}>
              Password
              <TextTypes type='h5' color={COLOURS.primary}>
                *
              </TextTypes>
            </TextTypes>
            <View style={[INPUT_STYLES.textInputView, pv12]}>
              <TextInput
                style={flex1}
                placeholder='Enter password'
                value={values.password}
                onChangeText={handleChange('password')}
                onBlur={handleBlur('password')}
                placeholderTextColor={COLOURS.greyDark}
                secureTextEntry={!values.showPassword}
                autoCapitalize='none'
              />
              <TouchableOpacity
                onPress={() =>
                  setFieldValue('showPassword', !values.showPassword)
                }
              >
                <ShowEye
                  color={
                    values.showPassword ? COLOURS.primary : COLOURS.grayIcon
                  }
                />
              </TouchableOpacity>
            </View>
            {(touched.password || submitCount > 0) && errors.password && (
              <View style={errorRow}>
                <ErrorIcon style={{ marginRight: 6 }} />
                <TextTypes type='errorText' color={COLOURS.errorText}>
                  {typeof errors.password === 'string' ? errors.password : ''}
                </TextTypes>
              </View>
            )}
          </View>
          <View style={formGroup}>
            <TextTypes type='h5' color={COLOURS.textBlack}>
              Your role
              <TextTypes type='h5' color={COLOURS.primary}>
                *
              </TextTypes>
            </TextTypes>
            <Dropdown
              style={[
                INPUT_STYLES.textInputView,
                Platform.OS === 'android' && pv12,
              ]}
              data={vetRoles}
              labelField='label'
              valueField='value'
              placeholder='Select role'
              placeholderStyle={placeholderStyle}
              value={values.role}
              onChange={(item) => setFieldValue('role', item.value)}
              renderRightIcon={() => (
                <Ionicons
                  name='chevron-down'
                  size={18}
                  color={COLOURS.grayIcon}
                />
              )}
            />
            {(touched.role || submitCount > 0) && errors.role && (
              <View style={errorRow}>
                <ErrorIcon style={{ marginRight: 6 }} />
                <TextTypes type='errorText' color={COLOURS.errorText}>
                  {typeof errors.role === 'string' ? errors.role : ''}
                </TextTypes>
              </View>
            )}
          </View>
          <TextTypes type='h5' color={COLOURS.textBlack}>
            Affiliated practice
          </TextTypes>
          <View style={practiceBox}>
            <TextTypes
              type='h5'
              customStyle={practiceName}
              color={COLOURS.textBlack}
            >
              {affiliatedPractice.name}
            </TextTypes>
            <View style={affiliateInfo}>
              <LocationIcon />
              <TextTypes type='errorText' color={COLOURS.secondaryTint}>
                {affiliatedPractice.address}
              </TextTypes>
            </View>
            <View style={affiliateInfo}>
              <CallIcon />
              <TextTypes type='errorText' color={COLOURS.secondaryTint}>
                {affiliatedPractice.phone || '*************'}
              </TextTypes>
            </View>
            <TouchableOpacity onPress={onEditClick}>
              <TextTypes
                type='buttonText'
                color={COLOURS.primary}
                customStyle={editButtonText}
              >
                EDIT
              </TextTypes>
            </TouchableOpacity>
          </View>
          <TextTypes
            type='label'
            customStyle={autoFilled}
            color={COLOURS.secondaryTint}
          >
            Auto-filled from QR code
          </TextTypes>
          <View style={infoBox}>
            <Ionicons
              name='information-circle-outline'
              size={18}
              color={COLOURS.greenText}
              style={infoIcon}
            />
            <TextTypes
              color={COLOURS.primary}
              type='small'
              customStyle={infoText}
            >
              You can add more detailed professional info later – it’s needed
              for certain collaborations and independent consultation
              opportunities.
            </TextTypes>
          </View>
          <View style={checkboxRow}>
            <Checkbox
              value={values.agree}
              onValueChange={(val) => setFieldValue('agree', val)}
              color={values.agree ? COLOURS.greenText : undefined}
              style={checkbox}
            />
            <TextTypes type='body3' color={COLOURS.textBlack}>
              I agree to the Vet Assist{' '}
              <TextTypes type='body3' color={COLOURS.primary}>
                Terms of Use
              </TextTypes>{' '}
              and{' '}
              <TextTypes type='body3' color={COLOURS.primary}>
                Privacy Policy
              </TextTypes>
              .
            </TextTypes>
          </View>
          {(touched.agree || submitCount > 0) && errors.agree && (
            <View style={errorRow}>
              <ErrorIcon style={{ marginRight: 6 }} />
              <TextTypes type='errorText' color={COLOURS.errorText}>
                {typeof errors.agree === 'string' ? errors.agree : ''}
              </TextTypes>
            </View>
          )}
          <Button
            title='Continue to Chat'
            onPress={handleSubmit as any}
            disabled={!isValid}
            textStyle={btnText}
          />
        </ScrollView>
      )}
    </Formik>
  );
}
