import * as Yup from 'yup';

export const vetRoles = [
  { label: 'Veterinarian', value: 'vet' },
  { label: 'Vet Nurse', value: 'vet_nurse' },
  { label: 'Vet Student', value: 'vet_student' },
  { label: 'Vet Nurse Student', value: 'vet_nurse_student' },
  { label: 'Other', value: 'other' },
];

export const vetProfessionalValidationSchema = Yup.object().shape({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string()
    .matches(
      /^[\w-.]+@[\w-]+\.[a-zA-Z]{2,}$/,
      'Invalid email address'
    )
    .required('Email is required'),
  password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
  role: Yup.string().required('Role is required'),
  agree: Yup.boolean().oneOf([true], 'You must agree to the terms'),
});
