import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  flex1: { flex: 1 },
  pv12: { paddingVertical: 12 },
  placeholderStyle: { color: COLOURS.greyDark },
  wrapper: {
    paddingHorizontal: 24,
    paddingTop: 16,
    backgroundColor: COLOURS.white,
    paddingBottom: 60,
  },
  formGroup: {
    marginBottom: 16,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 4,
  },
  title: {
    marginBottom: 16,
    marginTop: 8,
  },
  subtitle: {
    marginBottom: 16,
  },
  errorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    marginBottom: 10,
  },
  affiliateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 6,
  },
  practiceBox: {
    backgroundColor: COLOURS.greyLight,
    borderRadius: 12,
    padding: 12,
    marginTop: 8,
    marginBottom: 4,
  },
  practiceName: {
    marginBottom: 8
  },
  editButtonText: {
    alignSelf: 'center',
    marginTop: 6
  },
  autoFilled: {
    marginBottom: 8,
  },
  infoIcon: { marginRight: 6 },
  infoBox: {
    backgroundColor: COLOURS.infoBoxColor,
    borderRadius: 12,
    flexDirection: 'row',
    padding: 12,
    paddingVertical: 15,
    marginVertical: 16
  },
  infoText: {
    lineHeight: 18,
  },

  checkboxRow: {
    flexDirection: 'row',
    marginBottom: 8,
    marginTop: 2,
  },
  checkbox: {
    marginRight: 8,
    width: 20,
    height: 20,
    borderRadius: 4,
  },
  btnText: {
    textTransform: 'uppercase'
  }
});

export default styles; 