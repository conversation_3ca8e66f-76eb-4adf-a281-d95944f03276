import { getS3SignedUrl } from '@/lib/vet247/documents';
import { uploadToS3 } from '@/lib/user/uploadUtils';
import { register } from '@/lib/auth/register';
import { addPet } from '@/lib/user/userPets';
import { registerVetProfessional } from '@/lib/vet/register';
import type { OwnerData } from '@/types/owner';
import type { PetData } from '@/types/pet';

export const defaultOwnerData: OwnerData = {
  firstName: '',
  lastName: '',
  email: '',
  password: '',
  referral: '',
  confirmPassword: '',
  agreed: false,
};

export const defaultPetData: PetData = {
  photo: null,
  name: '',
  species: '',
  breed: '',
  sex: '',
  weight: '',
  weightUnit: 'kg',
  dob: null,
  showDate: false,
  tempDate: null,
  petPicture: null
}; 

export const PET_OWNER_STEPS = [
    'choosePath', // 1
    'petOwnerAccount', // 2
    'petDetails', // 3
];

export const vetProfessionalInitialValues = {
  firstName: '',
  lastName: '',
  email: '',
  password: '',
  role: '',
  agree: false,
  showPassword: false,
};

// Type for affiliated practice API response
export type AffiliatedPracticeApi = {
  id: number;
  name: string;
  adminId: number | null;
  approved: boolean;
  email: string | null;
  contactNumber: string | null;
  accessCode: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  address: {
    id: number;
    addressLine1: string;
    addressLine2: string | null;
    postcode: string;
    city: string | null;
    county: string;
    country: string;
    countryCode: string | null;
    createdAt: string;
    updatedAt: string;
  };
};

export const VET_PROFESSIONAL_STEPS = [
    'choosePath', // 1
    'vetProCode', // 2
    'vetProfessionalAccount', // 3 (new step for vet professional account form)
    // Add more steps for vetProfessional here if needed
];

export async function uploadPetImage(photo: string, photoFileName: string | undefined, photoFileType: string | undefined, userId: string, accessToken: string): Promise<string> {
  const originalFileName = photoFileName || photo.split('/').pop() || `profile-pic.jpg`;
  const fileType = photoFileType || 'image/jpeg';
  const formattedFileName = `${userId}-pet_picture-${originalFileName}`;
  const signedUrl = await getS3SignedUrl(formattedFileName, fileType, accessToken);
  await uploadToS3(signedUrl, photo, fileType);
  return formattedFileName;
}

export async function handlePetContinueService(
  ownerData: OwnerData,
  petData: PetData,
  {
    onSuccess,
    onError,
  }: { onSuccess: (user: any, accessToken: string) => Promise<void> | void; onError: (err: any) => void }
) {
  try {
    const regResult = await register(
      ownerData.firstName,
      ownerData.lastName,
      ownerData.email,
      ownerData.password,
      ownerData.referral
    );
    const { accessToken, user } = regResult;

    let petDataToSend = { ...petData };
    if (petData.photo && !petData.photo.startsWith('http')) {
      const fileName = await uploadPetImage(
        petData.photo,
        petData.photoFileName,
        petData.photoFileType,
        user.sub,
        accessToken
      );
      petDataToSend.petPicture = fileName;
      petDataToSend.photo = fileName;
    }
    await addPet(user.sub, accessToken, petDataToSend);
    await onSuccess(user, accessToken);
  } catch (err) {
    onError(err);
  }
}

export async function handleVetProfessionalContinueService(
  vetProfessionalData: any,
  {
    onSuccess,
    onError,
  }: { onSuccess: (user: any, accessToken: string) => Promise<void> | void; onError: (err: any) => void }
) {
  try {
    const regResult = await registerVetProfessional(
      vetProfessionalData.firstName,
      vetProfessionalData.lastName,
      vetProfessionalData.email,
      vetProfessionalData.password,
      vetProfessionalData.role,
      vetProfessionalData.practiceId
    );
    const { accessToken, user } = regResult;
    await onSuccess(user, accessToken);
  } catch (err) {
    onError(err);
  }
}
