import { Ionicons } from '@expo/vector-icons';
import { Formik } from 'formik';
import {
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';

import { AddPhotoIcon, StarIcon } from '@/components/icons';
import ErrorIcon from '@/components/icons/Error';
import TextTypes from '@/components/text-types';
import DatePickerField from '@/components/ui/date-picker-field';
import { COLOURS } from '@/constants/colours';
import { INPUT_STYLES } from '@/constants/formInputs';
import { SEXES, SPECIES, WEIGHT_UNITS } from '@/constants/pets';

import type { Props } from './service';
import { validationSchema } from './service';
import { pickPetImage } from './service';
import styles from './style';

export default function StepPetDetails({
  onContinue,
  formData,
  setFormData,
}: Props) {
  const {
    wrapper,
    subtitle,
    title,
    photoCircle,
    photo,
    formGroup,
    errorRow,
    dropDownView,
    placeholderStyle,
    textStyle,
    textInput,
    weightInput,
    verticalDivider,
    dropDownStyle,
    dropDownContainerStyle,
    infoBox,
    upgradeBox,
    continueBtn,
    continueBtnDisabled,
    addPhotoText,
    instructionText,
    infoIcon,
    upgradeText,
    dividerLine
  } = styles;
  const { textInputView } = INPUT_STYLES;
  return (
    <Formik
      initialValues={formData}
      enableReinitialize
      validationSchema={validationSchema}
      validateOnMount
      onSubmit={(values) => {
        setFormData(values);
        onContinue(values);
      }}
    >
      {({
        handleChange,
        handleBlur,
        handleSubmit,
        values,
        errors,
        touched,
        setFieldValue,
        isValid,
        submitCount,
      }) => {
        // Handler for picking an image using the service function
        const handlePickImage = async () => {
          await pickPetImage({
            onPicked: ({ uri, fileName, fileType }) => {
              setFieldValue('photo', uri);
              setFieldValue('photoFileName', fileName);
              setFieldValue('photoFileType', fileType);
              setFormData({
                ...values,
                photo: uri,
                photoFileName: fileName,
                photoFileType: fileType,
              });
            },
            onError: (msg) => alert(msg),
          });
        };
        return (
          <KeyboardAvoidingView
            style={{ flex: 1 }}
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          >
            <ScrollView
              contentContainerStyle={wrapper}
              keyboardShouldPersistTaps='handled'
              showsVerticalScrollIndicator={false}
            >
              <TextTypes type='h1' color={COLOURS.primary} customStyle={title}>
                Let's meet your pet
              </TextTypes>
              <TextTypes
                type='body2'
                color={COLOURS.secondaryTint}
                customStyle={subtitle}
              >
                Add your pet's details so VetAssist can provide the best
                personalised care and advice.
              </TextTypes>
              <TouchableOpacity
                style={photoCircle}
                onPress={handlePickImage}
                activeOpacity={0.8}
              >
                {values.photo ? (
                  <Image source={{ uri: values.photo }} style={photo} />
                ) : (
                  <>
                    <AddPhotoIcon />
                    <TextTypes
                      customStyle={addPhotoText}
                      type='errorText'
                      color={COLOURS.primary}
                    >
                      add photo
                    </TextTypes>
                  </>
                )}
              </TouchableOpacity>
              <View style={formGroup}>
                <TextTypes type='h5' color={COLOURS.textBlack}>
                  Pet’s name
                  <TextTypes type='h5' color={COLOURS.primary}>
                    *
                  </TextTypes>
                </TextTypes>
                <TextInput
                  style={textInput}
                  placeholder='Type name'
                  value={values.name}
                  onChangeText={(text) => {
                    handleChange('name')(text);
                    setFormData({ ...values, name: text });
                  }}
                  onBlur={handleBlur('name')}
                  placeholderTextColor={COLOURS.grayIcon}
                />
                {(touched.name || submitCount > 0) && errors.name && (
                  <View style={errorRow}>
                    <ErrorIcon style={{ marginRight: 6 }} />
                    <TextTypes type='errorText' color={COLOURS.errorText}>
                      {errors.name}
                    </TextTypes>
                  </View>
                )}
              </View>
              <View style={formGroup}>
                <TextTypes type='h5' color={COLOURS.textBlack}>
                  Species
                  <TextTypes type='h5' color={COLOURS.primary}>
                    *
                  </TextTypes>
                </TextTypes>
                <Dropdown
                  style={[textInputView, dropDownView]}
                  data={SPECIES.map((s) => ({ label: s, value: s }))}
                  labelField='label'
                  valueField='value'
                  placeholder='Select species'
                  placeholderStyle={placeholderStyle}
                  value={values.species}
                  selectedTextStyle={textStyle}
                  itemTextStyle={textStyle}
                  onChange={(item) => {
                    setFieldValue('species', item.value);
                    setFieldValue('breed', '');
                    setFormData({ ...values, species: item.value, breed: '' });
                  }}
                  renderRightIcon={() => (
                    <Ionicons
                      name='chevron-down'
                      size={18}
                      color={COLOURS.grayIcon}
                    />
                  )}
                />
                {(touched.species || submitCount > 0) && errors.species && (
                  <View style={errorRow}>
                    <ErrorIcon style={{ marginRight: 6 }} />
                    <TextTypes type='errorText' color={COLOURS.errorText}>
                      {errors.species}
                    </TextTypes>
                  </View>
                )}
              </View>
              <View style={formGroup}>
                <TextTypes
                  type='h5'
                  color={values.species ? undefined : COLOURS.diableText}
                >
                  Breed
                  <TextTypes type='h5' color={COLOURS.primary}>
                    *
                  </TextTypes>
                </TextTypes>
                <TextInput
                  style={textInput}
                  placeholder='Type breed'
                  value={values.breed}
                  onChangeText={(text) => {
                    handleChange('breed')(text);
                    setFormData({ ...values, breed: text });
                  }}
                  onBlur={handleBlur('breed')}
                  placeholderTextColor={COLOURS.grayIcon}
                  editable={!!values.species}
                />
                {(touched.breed || submitCount > 0) && errors.breed && (
                  <View style={errorRow}>
                    <ErrorIcon style={{ marginRight: 6 }} />
                    <TextTypes type='errorText' color={COLOURS.errorText}>
                      {errors.breed}
                    </TextTypes>
                  </View>
                )}
              </View>
              <View style={formGroup}>
                <TextTypes type='h5' color={COLOURS.textBlack}>
                  Pet’s sex
                  <TextTypes type='h5' color={COLOURS.primary}>
                    *
                  </TextTypes>
                </TextTypes>
                <Dropdown
                  style={[textInputView, dropDownView]}
                  data={SEXES.map((s) => ({ label: s, value: s }))}
                  labelField='label'
                  valueField='value'
                  selectedTextStyle={textStyle}
                  itemTextStyle={textStyle}
                  placeholderStyle={placeholderStyle}
                  placeholder='Select sex'
                  value={values.sex}
                  onChange={(item) => {
                    setFieldValue('sex', item.value);
                    setFormData({ ...values, sex: item.value });
                  }}
                  renderRightIcon={() => (
                    <Ionicons
                      name='chevron-down'
                      size={18}
                      color={COLOURS.grayIcon}
                    />
                  )}
                />
                {(touched.sex || submitCount > 0) && errors.sex && (
                  <View style={errorRow}>
                    <ErrorIcon style={{ marginRight: 6 }} />
                    <TextTypes type='errorText' color={COLOURS.errorText}>
                      {errors.sex}
                    </TextTypes>
                  </View>
                )}
              </View>
              <View style={formGroup}>
                <TextTypes type='h5' color={COLOURS.textBlack}>
                  Pet’s weight
                </TextTypes>
                <View style={textInput}>
                  <TextInput
                    style={weightInput}
                    placeholder='Enter weight'
                    value={values.weight}
                    onChangeText={(text) => {
                      handleChange('weight')(text);
                      setFormData({ ...values, weight: text });
                    }}
                    placeholderTextColor={COLOURS.grayIcon}
                    keyboardType='numeric'
                  />
                  <View style={verticalDivider} />
                  <Dropdown
                    style={dropDownStyle}
                    containerStyle={dropDownContainerStyle}
                    data={WEIGHT_UNITS.map((u) => ({ label: u, value: u }))}
                    labelField='label'
                    selectedTextStyle={textStyle}
                    itemTextStyle={textStyle}
                    valueField='value'
                    placeholder='kg'
                    value={values.weightUnit}
                    onChange={(item) => {
                      setFieldValue('weightUnit', item.value);
                      setFormData({ ...values, weightUnit: item.value });
                    }}
                    renderRightIcon={() => (
                      <Ionicons
                        name='chevron-down'
                        size={18}
                        color={COLOURS.grayIcon}
                      />
                    )}
                  />
                </View>
              </View>
              <DatePickerField
                value={values.dob ? new Date(values.dob) : undefined}
                onChange={(date) => {
                  setFieldValue('dob', date);
                  setFormData({ ...values, dob: date });
                }}
                label='Date of birth'
                placeholder='DD/MM/YYYY'
                maximumDate={new Date()}
              />
              <View style={infoBox}>
                <Ionicons
                  name='information-circle-outline'
                  size={18}
                  color={COLOURS.greenText}
                  style={infoIcon}
                />
                <TextTypes
                  color={COLOURS.primary}
                  type='small'
                  customStyle={instructionText}
                >
                  You can add more detailed info later - like microchip,
                  insurance, and your vet details
                </TextTypes>
              </View>
              <View style={upgradeBox}>
                <StarIcon />
                <TextTypes
                  customStyle={upgradeText}
                  color={COLOURS.upgradeText}
                  type='small'
                >
                  Upgrade to one of our{' '}
                  <TextTypes type='errorText'>VetAssist Plans</TextTypes> to add
                  more pets and access premium features!
                </TextTypes>
              </View>
            <View style={dividerLine} />
              <TouchableOpacity
                style={[continueBtn, !isValid && continueBtnDisabled]}
                disabled={!isValid}
                onPress={handleSubmit as any}
              >
                <TextTypes
                  type='buttonText'
                  color={isValid ? COLOURS.white : COLOURS.grayIcon}
                >
                  CONTINUE TO CHAT
                </TextTypes>
              </TouchableOpacity>
            </ScrollView>
          </KeyboardAvoidingView>
        );
      }}
    </Formik>
  );
}
