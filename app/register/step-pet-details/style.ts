import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  dividerLine: {
    backgroundColor: COLOURS.greyLight,
    height: 1,
    marginHorizontal: -25,
    marginBottom: 8,
  },
  infoIcon: { marginRight: 6 },
  instructionText: {
    lineHeight: 18
  },
  addPhotoText: {
    lineHeight: 18,
    marginTop: 8
  },
  title: {
    marginBottom: 16,
    marginTop: 8,
  },
  upgradeText: {
    flex: 1,
    marginLeft: 6,
    lineHeight: 18
  },
  subtitle: {
    marginBottom: 16,
  },
  weightInput: { flex: 1, marginRight: 8, paddingTop: 0, paddingBottom: 0 },
  dropDownView: {
    paddingVertical: 15,
  },
  textStyle: {
    fontFamily: 'Comfortaa-SemiBold',
    fontSize: 16,
    lineHeight: 24,
    color: COLOURS.textBlack,
    fontWeight: '600',
  },
  verticalDivider: {
    width: 1,
    backgroundColor: COLOURS.borderColor,
    height: '160%',
    marginRight: 7,
  },
  dropDownContainerStyle: { width: 60 },
  dropDownStyle: { width: 40, marginRight: 0 },
  textInput: {
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 16,
    padding: 16,
    marginVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  placeholderStyle: {
    color: COLOURS.grayIcon,
  },
  wrapper: {
    paddingBottom: 60,
    paddingHorizontal: 24,
    paddingTop: 16,
    backgroundColor: COLOURS.white,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  backBtn: {
    padding: 4,
  },
  languageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  photoCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    marginBottom: 18,
    marginTop: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
  },
  photo: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  formGroup: {
    marginBottom: 12,
  },
  chipSelected: {
    backgroundColor: COLOURS.greenText,
    borderColor: COLOURS.greenText,
  },
  infoBox: {
    backgroundColor: COLOURS.infoBoxColor,
    borderRadius: 12,
    flexDirection: 'row',
    padding: 12,
    paddingVertical: 15,
    marginBottom: 12,
    marginTop: 5,
  },
  upgradeBox: {
    backgroundColor: COLOURS.uppgradeBoxColor,
    borderRadius: 12,
    flexDirection: 'row',
    padding: 12,
    paddingVertical: 15,
    marginBottom: 16,
  },
  continueBtn: {
    backgroundColor: COLOURS.greenText,
    borderRadius: 32,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 14,
  },
  continueBtnDisabled: {
    backgroundColor: COLOURS.borderColor,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: COLOURS.backgroundModal,
    justifyContent: 'flex-end',
  },
  errorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
});

export default styles; 