import * as Yup from 'yup';
import * as ImagePicker from 'expo-image-picker';
import type { PetData } from '@/types/pet';

export interface Props {
  onContinue: (data: PetData) => void;
  formData: PetData;
  setFormData: (data: PetData) => void;
}

export const validationSchema = Yup.object().shape({
  name: Yup.string().required('Pet name is required'),
  species: Yup.string().required('Species is required'),
  breed: Yup.string().required('Breed is required'),
  sex: Yup.string().required('Sex is required'),
});

export async function pickPetImage({
  onPicked,
  onError,
}: {
  onPicked: (file: { uri: string; fileName: string; fileType: string }) => void;
  onError?: (error: string) => void;
}) {
  const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
  if (status !== 'granted') {
    onError?.('Sorry, we need camera roll permissions to make this work!');
    return;
  }
  const result = await ImagePicker.launchImageLibraryAsync({
    mediaTypes: ImagePicker.MediaTypeOptions.Images,
    allowsEditing: true,
    aspect: [1, 1],
    quality: 0.7,
  });
  if (!result.canceled && result.assets && result.assets.length > 0) {
    const asset = result.assets[0];
    const fileUri = asset.uri;
    const fileName = asset.fileName || fileUri.split('/').pop() || 'profile-pic.jpg';
    const fileType = asset.type || 'image/jpeg';
    onPicked({ uri: fileUri, fileName, fileType });
  }
} 