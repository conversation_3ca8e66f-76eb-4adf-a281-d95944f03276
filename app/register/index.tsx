import { useRouter } from 'expo-router';
import { useState } from 'react';
import type { AffiliatedPracticeApi } from './services';
import { Text, TouchableOpacity, View, Modal, Pressable } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import FullScreenLoader from '@/components/ui/FullScreenLoader';
import AlertIcon from '@/components/icons/Alert';

import { ArrowLeft, Language } from '@/components/icons';
import { COLOURS } from '@/constants/colours';
import TextTypes from '@/components/text-types';

import StepChoosePath from './step-choose-path';
import StepPetDetails from './step-pet-details';
import StepPetOwnerAccount from './step-pet-owner-account';
import StepVetProCode from './step-vet-pro-code';
import StepVetProfessionalAccount from './step-vet-professional-account';
import { useAuth } from '@/context/auth';
import type { PetData } from '@/types/pet';
import type { OwnerData } from '@/types/owner';
import styles from './styles';
import { handlePetContinueService, defaultOwnerData, defaultPetData, PET_OWNER_STEPS, VET_PROFESSIONAL_STEPS, vetProfessionalInitialValues, handleVetProfessionalContinueService } from './services';

export default function Register() {
    const { container, headerRow, backBtn, languageRow, progressBarBg, progressBarFill, modalOverlay, modalContent, modalIcon, modalErrorText, modalButton, modalButtonText, languageTextButton } = styles;
    const [step, setStep] = useState(1);
    const [userType, setUserType] = useState<'petOwner' | 'vetProfessional' | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const router = useRouter();
    const {login: loginUser} = useAuth();

    // Determine steps and total steps based on userType
    const steps = userType === 'vetProfessional' ? VET_PROFESSIONAL_STEPS : PET_OWNER_STEPS;
    const TOTAL_STEPS = steps.length;
    const progress = step / TOTAL_STEPS;

    const handleBack = () => {
        if (step === 1) {
            router.back();
        } else {
            setStep(step - 1);
        }
    };

    const [ownerData, setOwnerData] = useState<OwnerData>(defaultOwnerData);
    const [petData, setPetData] = useState<PetData>(defaultPetData);
    const [vetProCode, setVetProCode] = useState('');
    const [vetProfessionalFormData, setVetProfessionalFormData] = useState(vetProfessionalInitialValues);
    const [affiliatedPractice, setAffiliatedPractice] = useState<AffiliatedPracticeApi | null>(null);



    const handleOwnerContinue = (data: OwnerData) => {
        setOwnerData(data);
        setStep(3);
    };

    const handlePetContinue = async (data: PetData) => {
        setPetData(data);
        setLoading(true);
        setError(null);
        await handlePetContinueService(
            ownerData,
            data,
            {
                onSuccess: async () => {
                    await loginUser();
                    setLoading(false);
                    router.replace('/(tabs)');
                },
                onError: (err) => {
                    setError(err.message);
                    setLoading(false);
                },
            }
        );
    };

    const handleVetProfessionalContinue = async (data: any) => {
        if (!affiliatedPractice?.id) {
            setError('Invalid access code');
            return;
        }
        setVetProfessionalFormData(data);
        setLoading(true);
        setError(null);
        data.practiceId = affiliatedPractice?.id;
        await handleVetProfessionalContinueService(
            data,
            {
                onSuccess: async () => {
                    await loginUser();
                    setLoading(false);
                    router.replace('/(tabs)');
                },
                onError: (err: any) => {
                    if (err instanceof Error) {
                        setError(err.message);
                    } else {
                        setError('Registration failed');
                    }
                    setLoading(false);
                },
            }
        );
    };

    return (
        <SafeAreaView style={container} edges={['top']}>
            <View style={headerRow}>
                <TouchableOpacity style={backBtn} onPress={handleBack}>
                    <ArrowLeft color={COLOURS.primary} />
                </TouchableOpacity>
                <View style={{ flex: 1 }} />
                <View style={languageRow}>
                    <Language color={COLOURS.primary} />
                    <TextTypes type='buttonText' color={COLOURS.primary} customStyle={languageTextButton}>
                        EN
                    </TextTypes>
                </View>
            </View>
            <View style={progressBarBg}>
                <View style={[progressBarFill, { flex: progress }]} />
                <View style={{ flex: 1 - progress }} />
            </View>
            {/* Step 1: Choose Path (always first) */}
            <View style={{ flex: 1, display: step === 1 ? 'flex' : 'none' }}>
                <StepChoosePath
                    userType={userType}
                    onSelect={(type) => {
                        setUserType(type);
                        setTimeout(() => {
                            setStep(2);
                        }, 300);
                    }}
                />
            </View>
            {/* Step 2: Vet Pro Code or Pet Owner Account */}
            {userType === 'vetProfessional' && (
                <View style={{ flex: 1, display: step === 2 ? 'flex' : 'none' }}>
                    <StepVetProCode
                        code={vetProCode}
                        setCode={setVetProCode}
                        onContinue={(validCode: string, practiceData: AffiliatedPracticeApi) => {
                            setVetProCode(validCode);
                            setAffiliatedPractice(practiceData);
                            setStep(3);
                        }}
                    />
                </View>
            )}
            {userType === 'petOwner' && (
                <View style={{ flex: 1, display: step === 2 ? 'flex' : 'none' }}>
                    <StepPetOwnerAccount
                        onContinue={handleOwnerContinue}
                        formData={ownerData}
                        setFormData={setOwnerData}
                    />
                </View>
            )}
            {/* Step 3: Pet Details (petOwner) or Vet Professional Account (vetProfessional) */}
            {userType === 'petOwner' && (
                <View style={{ flex: 1, display: step === 3 ? 'flex' : 'none' }}>
                    <StepPetDetails
                        onContinue={handlePetContinue}
                        formData={petData}
                        setFormData={setPetData}
                    />
                </View>
            )}
            {userType === 'vetProfessional' && (
                <View style={{ flex: 1, display: step === 3 ? 'flex' : 'none' }}>
                    {affiliatedPractice && (
                        <StepVetProfessionalAccount
                            onContinue={handleVetProfessionalContinue}
                            onEditClick={handleBack}
                            affiliatedPractice={{
                                name: affiliatedPractice.name,
                                address: [
                                    affiliatedPractice.address?.addressLine1,
                                    affiliatedPractice.address?.county,
                                    affiliatedPractice.address?.postcode,
                                ].filter(Boolean).join(', '),
                                phone: affiliatedPractice.contactNumber || '',
                            }}
                            initialValues={vetProfessionalFormData}
                        />
                    )}
                </View>
            )}
            <FullScreenLoader visible={loading} />
            <Modal
                visible={!!error}
                transparent
                animationType="fade"
                onRequestClose={() => setError(null)}
            >
                <View style={modalOverlay}>
                    <View style={modalContent}>
                        <AlertIcon width={56} height={56} style={modalIcon} color={COLOURS.errorText} />
                        <Text style={modalErrorText}>{error}</Text>
                        <Pressable style={modalButton} onPress={() => setError(null)}>
                            <Text style={modalButtonText}>OK</Text>
                        </Pressable>
                    </View>
                </View>
            </Modal>
        </SafeAreaView>
    );
}
