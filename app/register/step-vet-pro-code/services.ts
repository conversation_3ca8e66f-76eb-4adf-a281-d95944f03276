// Placeholder for StepVetProCode services

import { verifyAccessToken } from '@/lib/vet/verifyAccessToken';

export {};

export async function validateVetProCode(code: string): Promise<{ success: boolean; error?: string; data?: any }> {
  try {
    const data = await verifyAccessToken(code);
    return { success: true, data };
  } catch (error: any) {
    return { success: false, error: error.message || 'Unknown error' };
  }
} 