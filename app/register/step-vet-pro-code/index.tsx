import { useRouter } from 'expo-router';
import { useState } from 'react';
import { TextInput, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { QRCode, VetProIcon } from '@/components/icons';
import ErrorIcon from '@/components/icons/Error';
import TextTypes from '@/components/text-types';
import Button from '@/components/ui/button';
import { COLOURS } from '@/constants/colours';
import { INPUT_STYLES } from '@/constants/formInputs';
import QRScannerModal from '@/components/ui/qr-scanner-modal';

import { validateVetProCode } from './services';
import styles from './styles';

export default function StepVetProCode({
  code,
  setCode,
  onContinue,
}: {
  code: string;
  setCode: (code: string) => void;
  onContinue: (code: string, practiceData: any) => void;
}) {
  const router = useRouter();
  const insects = useSafeAreaInsets();
  const {
    container,
    logo,
    title,
    subtitle,
    body,
    inputRow,
    requestText,
    formGroup,
    codeInput,
    qrCode,
    mainContainer,
    btnText,
  } = styles;

  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [qrModalVisible, setQrModalVisible] = useState(false);

  const handleUnlock = async () => {
    setError(null);
    if (!code.trim()) return;
    setLoading(true);
    const result = await validateVetProCode(code.trim());
    setLoading(false);
    if (result.success) {
      onContinue(code.trim(), result.data);
    } else {
      setError(result.error || 'Invalid code');
    }
  };

  const handleQRScanSuccess = (data: string) => {
    setCode(data);
    if (error) setError(null);
  };

  return (
    <View style={container}>
      <View style={mainContainer}>
        <TextTypes type='h1' color={COLOURS.secondaryShade} customStyle={title}>
          Welcome to
        </TextTypes>
        <VetProIcon style={logo} />
        <TextTypes type='h2' color={COLOURS.primary} customStyle={subtitle}>
          Enter or scan your VetAssist Pro code
        </TextTypes>
        <TextTypes
          type='body2'
          color={COLOURS.secondaryTint}
          customStyle={body}
        >
          You’ll need a code to get started. If your clinic or organisation uses
          VetAssist Pro, they’ll have one - scan or enter it here to connect
          your account.
        </TextTypes>
        <TextTypes customStyle={formGroup} type='h5' color={COLOURS.textBlack}>
          Access code
        </TextTypes>
        <View style={inputRow}>
          <TextInput
            style={[INPUT_STYLES.textInput, codeInput]}
            placeholder='Enter your code'
            placeholderTextColor={COLOURS.greyDark}
            value={code}
            onChangeText={(v) => {
              setCode(v);
              if (error) setError(null);
            }}
            autoCapitalize='characters'
            editable={!loading}
          />
          <TouchableOpacity
            activeOpacity={0.8}
            style={qrCode}
            onPress={() => setQrModalVisible(true)}
            disabled={loading}
          >
            <QRCode />
          </TouchableOpacity>
        </View>
        {error && (
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 8,
            }}
          >
            <ErrorIcon style={{ marginRight: 6 }} />
            <TextTypes type='errorText' color={COLOURS.errorText}>
              {error}
            </TextTypes>
          </View>
        )}
      </View>
      <Button
        title={loading ? 'Checking...' : 'Unlock Access'}
        onPress={handleUnlock}
        textStyle={btnText}
        disabled={!code.trim() || loading}
      />
      <TextTypes
        type='body3'
        color={COLOURS.textBlack}
        customStyle={[requestText, { paddingBottom: insects.bottom }]}
      >
        Need a code? Request one{' '}
        <TextTypes type='body3' color={COLOURS.primary}>
          here
        </TextTypes>
      </TextTypes>
      
      <QRScannerModal
        visible={qrModalVisible}
        onClose={() => setQrModalVisible(false)}
        onScanSuccess={handleQRScanSuccess}
      />
    </View>
  );
}
