import { StyleSheet } from 'react-native';

import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  qrCode: { left: -1.5 },
  codeInput: {
    flex: 1,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    height: 50,
    marginVertical: 0,
  },
  formGroup: {
    marginBottom: 8,
    alignSelf: 'flex-start',
    marginTop: 12
  },
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
    padding: 24,
    backgroundColor: 'white',
  },
  logo: { marginVertical: 16, alignSelf: 'center' },
  title: {
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    marginBottom: 8,
    marginTop: 10,
    lineHeight: 28,
    alignSelf: 'flex-start',
  },
  body: {
    marginBottom: 24,
    alignSelf: 'flex-start',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginBottom: 16,
  },
  requestText: {
    marginTop: 16,
    textAlign: 'center',
  },
  mainContainer: {
    flex: 1
  },
  btnText: {
    textTransform: 'uppercase'
  }
});

export default styles;
