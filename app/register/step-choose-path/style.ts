import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 14,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  backBtn: {
    padding: 4,
  },
  languageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  card: {
    backgroundColor: COLOURS.white,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
    paddingVertical: 24,
    paddingHorizontal: '12%',
    alignItems: 'center',
    shadowColor: COLOURS.black,
    shadowOpacity: 0.03,
    shadowRadius: 8,
    marginTop: '9%',
    shadowOffset: { width: 0, height: 2 },
  },
  cardSelected: {
    backgroundColor: COLOURS.greenText,
    borderColor: COLOURS.greenText,
  },
  title: {
    marginBottom: 16,
    marginTop: 8,
  },
  subtitle: {
    marginBottom: 16,
  },
});

export default styles; 