import { View } from 'react-native';

import { PetOwner, VetProfessional } from '@/components/icons';
import TextTypes from '@/components/text-types';
import UserTypeCard from '@/components/forms/user-type';
import styles from './style';
import type { Props } from './service';
import { COLOURS } from '@/constants/colours';

export default function StepChoosePath({ userType, onSelect }: Props) {
  const { wrapper, title, subtitle } = styles;
  return (
    <View style={wrapper}>
      <TextTypes type='h1' color={COLOURS.primary} customStyle={title}>Choose your path!</TextTypes>
      <TextTypes type='body2' color={COLOURS.secondaryTint} customStyle={subtitle}>
        Let’s get started! Tell us who you are, so we can personalise your VetAssist experience
      </TextTypes>
      <UserTypeCard
        icon={<PetOwner />}
        title="I am a pet owner"
        description="Get trusted health insights, care reminders, and emergency guidance for your beloved pets"
        selected={userType === 'petOwner'}
        onPress={() => onSelect('petOwner')}
      />
      <UserTypeCard
        icon={<VetProfessional />}
        title="I am a vet professional"
        description="Access your AI clinical assistant, manage patient records, and provide better care"
        selected={userType === 'vetProfessional'}
        onPress={() => onSelect('vetProfessional')}
      />
    </View>
  );
}
