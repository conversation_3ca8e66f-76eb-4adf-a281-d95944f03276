import * as Yup from 'yup';
import type { OwnerData } from '@/types/owner';

export interface Props {
  onContinue: (data: OwnerData) => void;
  formData: OwnerData;
  setFormData: (data: OwnerData) => void;
}

export const validationSchema = Yup.object().shape({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string()
    .matches(
      /^[\w-.]+@[\w-]+\.[a-zA-Z]{2,}$/,
      'Invalid email address'
    )
    .required('Email is required'),
  password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password')], 'Passwords must match')
    .required('Confirm password is required'),
  referral: Yup.string(),
  agreed: Yup.boolean().oneOf([true], 'You must agree to the terms'),
}); 