import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  qrCode: {left: -1.5},
  hintTextLink: {
    lineHeight: 21,
    alignSelf: 'center',
    textAlign: 'center'
  },
  dividerLine: {
    backgroundColor: COLOURS.greyLight,
    height: 1,
    marginHorizontal: -25,
    marginBottom: 16,
  },
  codeInput: {
    flex: 1,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    height: 50,
    marginVertical: 0
  },
  hintText: {
    textAlign: 'left',
    marginTop: 0
  },
  wrapper: {
    paddingHorizontal: 24,
    paddingTop: 16,
    backgroundColor: COLOURS.white,
    paddingBottom: 60,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  backBtn: {
    padding: 4,
  },
  languageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  formGroup: {
    marginBottom: 16,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 4
  },
  eyeBtn: {
    padding: 8,
    marginLeft: 4,
  },
  checkboxRow: {
    flexDirection: 'row',
    marginBottom: 8,
    marginTop: 2,
  },
  checkbox: {
    marginRight: 8,
    width: 20,
    height: 20,
    borderRadius: 4,
  },
  continueBtn: {
    backgroundColor: COLOURS.greenText,
    borderRadius: 32,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 8,
    marginBottom: 14,
  },
  continueBtnDisabled: {
    backgroundColor: COLOURS.borderColor,
  },
  errorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    marginBottom: 10,
  },
  title: {
    marginBottom: 16,
    marginTop: 8,
  },
  subtitle: {
    marginBottom: 16,
  },
});

export default styles; 