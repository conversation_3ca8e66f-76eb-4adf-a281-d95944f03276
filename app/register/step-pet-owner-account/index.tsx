import Checkbox from 'expo-checkbox';
import { useRouter } from 'expo-router';
import { Formik } from 'formik';
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { useRef, useEffect, useState } from 'react';

import { QRCode } from '@/components/icons';
import ErrorIcon from '@/components/icons/Error';
import PasswordMatch from '@/components/icons/PasswordMatch';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';
import { INPUT_STYLES } from '@/constants/formInputs';
import QRScannerModal from '@/components/ui/qr-scanner-modal';

import type { Props } from './service';
import { validationSchema } from './service';
import styles from './style';

export default function StepPetOwnerAccount({
  onContinue,
  formData,
  setFormData,
}: Props) {
  const {
    title,
    subtitle,
    wrapper,
    formGroup,
    errorRow,
    inputRow,
    qrCode,
    hintText,
    checkboxRow,
    checkbox,
    dividerLine,
    continueBtn,
    continueBtnDisabled,
    hintTextLink,
  } = styles;
  const router = useRouter();
  const formikRef = useRef<any>(null);
  const [qrModalVisible, setQrModalVisible] = useState(false);

  const onLoginPress = () => {
    router.replace('/login')
  }

  const handleQRScanSuccess = (data: string) => {
    if (formikRef.current) {
      formikRef.current.setFieldValue('referral', data);
    }
  };

  return (
    <>
      <Formik
        innerRef={formikRef}
        initialValues={{ ...formData }}
        enableReinitialize={false}
        validationSchema={validationSchema}
        validateOnMount
        onSubmit={(values) => {
          setFormData(values);
          onContinue(values);
        }}
      >
        {({
          handleChange,
          handleBlur,
          handleSubmit,
          values,
          errors,
          touched,
          setFieldValue,
          isValid,
          submitCount,
        }) => (
          <KeyboardAvoidingView
            style={{ flex: 1 }}
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          >
            <ScrollView
              contentContainerStyle={[wrapper, { flexGrow: 1 }]}
              keyboardShouldPersistTaps='handled'
              showsVerticalScrollIndicator={false}
            >
              <TextTypes type='h1' color={COLOURS.primary} customStyle={title}>
                Create your account
              </TextTypes>
              <TextTypes
                type='body2'
                color={COLOURS.secondaryTint}
                customStyle={subtitle}
              >
                Now join other pet owners getting better pet care with VetAssist.
              </TextTypes>
              <View style={formGroup}>
                <TextTypes type='h5' color={COLOURS.textBlack}>
                  First name
                  <TextTypes type='h5' color={COLOURS.primary}>
                    *
                  </TextTypes>
                </TextTypes>
                <TextInput
                  style={INPUT_STYLES.textInput}
                  placeholder='Enter first name'
                  value={values.firstName}
                  onChangeText={(text) => {
                    handleChange('firstName')(text);
                    setFormData({ ...values, firstName: text });
                  }}
                  onBlur={handleBlur('firstName')}
                  placeholderTextColor={COLOURS.greyDark}
                  autoCapitalize='words'
                />
                {(touched.firstName || submitCount > 0) && errors.firstName && (
                  <View style={errorRow}>
                    <ErrorIcon style={{ marginRight: 6 }} />
                    <TextTypes type='errorText' color={COLOURS.errorText}>
                      {errors.firstName}
                    </TextTypes>
                  </View>
                )}
              </View>
              <View style={formGroup}>
                <TextTypes type='h5' color={COLOURS.textBlack}>
                  Last name
                  <TextTypes type='h5' color={COLOURS.primary}>
                    *
                  </TextTypes>
                </TextTypes>
                <TextInput
                  style={INPUT_STYLES.textInput}
                  placeholder='Enter last name'
                  value={values.lastName}
                  onChangeText={(text) => {
                    handleChange('lastName')(text);
                    setFormData({ ...values, lastName: text });
                  }}
                  onBlur={handleBlur('lastName')}
                  placeholderTextColor={COLOURS.greyDark}
                  autoCapitalize='words'
                />
                {(touched.lastName || submitCount > 0) && errors.lastName && (
                  <View style={errorRow}>
                    <ErrorIcon style={{ marginRight: 6 }} />
                    <TextTypes type='errorText' color={COLOURS.errorText}>
                      {errors.lastName}
                    </TextTypes>
                  </View>
                )}
              </View>
              <View style={formGroup}>
                <TextTypes type='h5' color={COLOURS.textBlack}>
                  Email address
                  <TextTypes type='h5' color={COLOURS.primary}>
                    *
                  </TextTypes>
                </TextTypes>
                <TextInput
                  placeholder='Email'
                  placeholderTextColor={COLOURS.greyDark}
                  keyboardType='email-address'
                  autoCapitalize='none'
                  onChangeText={(text) => {
                    handleChange('email')(text);
                    setFormData({ ...values, email: text });
                  }}
                  onBlur={handleBlur('email')}
                  value={values.email}
                  style={INPUT_STYLES.textInput}
                />
                {(touched.email || submitCount > 0) && errors.email && (
                  <View style={errorRow}>
                    <ErrorIcon style={{ marginRight: 6 }} />
                    <TextTypes type='errorText' color={COLOURS.errorText}>
                      {errors.email}
                    </TextTypes>
                  </View>
                )}
              </View>
              <View style={formGroup}>
                <TextTypes type='h5' color={COLOURS.textBlack}>
                  Password
                  <TextTypes type='h5' color={COLOURS.primary}>
                    *
                  </TextTypes>
                </TextTypes>
                <View style={INPUT_STYLES.textInputView}>
                  <TextInput
                    placeholder='Password'
                    placeholderTextColor={COLOURS.greyDark}
                    onChangeText={(text) => {
                      handleChange('password')(text);
                      setFormData({ ...values, password: text });
                    }}
                    onBlur={handleBlur('password')}
                    value={values.password}
                    secureTextEntry={true}
                    autoCapitalize='none'
                    style={{ flex: 1 }}
                  />
                </View>
                {(touched.password || submitCount > 0) && errors.password && (
                  <View style={errorRow}>
                    <ErrorIcon style={{ marginRight: 6 }} />
                    <TextTypes type='errorText' color={COLOURS.errorText}>
                      {errors.password}
                    </TextTypes>
                  </View>
                )}
              </View>
              <View style={formGroup}>
                <TextTypes type='h5' color={COLOURS.textBlack}>
                  Confirm password
                  <TextTypes type='h5' color={COLOURS.primary}>
                    *
                  </TextTypes>
                </TextTypes>
                <View style={INPUT_STYLES.textInputView}>
                  <TextInput
                    placeholder='Confirm password'
                    placeholderTextColor={COLOURS.greyDark}
                    onChangeText={(text) => {
                      handleChange('confirmPassword')(text);
                      setFormData({ ...values, confirmPassword: text });
                    }}
                    onBlur={handleBlur('confirmPassword')}
                    value={values.confirmPassword}
                    secureTextEntry={true}
                    autoCapitalize='none'
                    style={{ flex: 1 }}
                  />
                  {values.password &&
                    values.confirmPassword &&
                    values.password === values.confirmPassword && (
                      <PasswordMatch style={{ marginLeft: 8 }} />
                    )}
                </View>
                {(touched.confirmPassword || submitCount > 0) &&
                  errors.confirmPassword && (
                    <View style={errorRow}>
                      <ErrorIcon style={{ marginRight: 6 }} />
                      <TextTypes type='errorText' color={COLOURS.errorText}>
                        {errors.confirmPassword}
                      </TextTypes>
                    </View>
                  )}
              </View>
              <View style={formGroup}>
                <TextTypes type='h5' color={COLOURS.textBlack}>
                  Do you have a referral code?
                </TextTypes>
                <View style={inputRow}>
                  <TextInput
                    style={[INPUT_STYLES.textInput, styles.codeInput]}
                    placeholder='Enter your code'
                    value={values.referral}
                    onChangeText={(text) => {
                      handleChange('referral')(text);
                      setFormData({ ...values, referral: text });
                    }}
                    onBlur={handleBlur('referral')}
                    placeholderTextColor={COLOURS.greyDark}
                    autoCapitalize='characters'
                  />
                  <TouchableOpacity
                    activeOpacity={0.8}
                    style={qrCode}
                    onPress={() => setQrModalVisible(true)}
                  >
                    <QRCode />
                  </TouchableOpacity>
                </View>
                <TextTypes
                  customStyle={hintText}
                  color={COLOURS.youText}
                  type='small'
                >
                  Provided by your vet, insurer, or group
                </TextTypes>
              </View>
              <View style={checkboxRow}>
                <Checkbox
                  value={values.agreed}
                  onValueChange={(val: boolean) => setFieldValue('agreed', val)}
                  color={values.agreed ? COLOURS.greenText : undefined}
                  style={checkbox}
                />
                <TextTypes type='body3' color={COLOURS.textBlack}>
                  I agree to the Vet Assist{' '}
                  <TextTypes type='body3' color={COLOURS.primary}>
                    Terms of Use
                  </TextTypes>{' '}
                  and{' '}
                  <TextTypes type='body3' color={COLOURS.primary}>
                    Privacy Policy
                  </TextTypes>
                  .
                </TextTypes>
              </View>
              {(touched.agreed || submitCount > 0) && errors.agreed && (
                <View style={errorRow}>
                  <ErrorIcon style={{ marginRight: 6 }} />
                  <TextTypes type='errorText' color={COLOURS.errorText}>
                    {errors.agreed}
                  </TextTypes>
                </View>
              )}
              <View style={dividerLine} />
              <TouchableOpacity
                style={[continueBtn, !isValid && continueBtnDisabled]}
                disabled={!isValid}
                onPress={handleSubmit as any}
              >
                <TextTypes
                  type='buttonText'
                  color={isValid ? COLOURS.white : COLOURS.grayIcon}
                >
                  CONTINUE TO PET DETAILS
                </TextTypes>
              </TouchableOpacity>
              {/* Login Link */}
              <TextTypes
                customStyle={hintTextLink}
                type='body3'
                color={COLOURS.textBlack}
              >
                Already have a Vet24-7{'\n'}account?{' '}
                <TextTypes onPress={onLoginPress} color={COLOURS.primary} type='h5'>Login</TextTypes>
              </TextTypes>
            </ScrollView>
          </KeyboardAvoidingView>
        )}
      </Formik>
      
      <QRScannerModal
        visible={qrModalVisible}
        onClose={() => setQrModalVisible(false)}
        onScanSuccess={handleQRScanSuccess}
      />
    </>
  );
}
