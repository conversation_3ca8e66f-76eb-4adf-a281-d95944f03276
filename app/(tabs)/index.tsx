import * as Clipboard from 'expo-clipboard';
import { useEffect, useRef, useState } from 'react';
import {
  FlatList,
  Image,
  KeyboardAvoidingView,
  Platform,
  View,
} from 'react-native';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';

import Header from '@/components/header';
import TextTypes from '@/components/text-types';
import Loader from '@/components/ui/Loader';
import ChatInterface from '@/components/ui/chat-interface';
import { COLOURS } from '@/constants/colours';
import { GLOBAL_STYLES } from '@/constants/globalStyles';
import { useAuth } from '@/context/auth';
import { generateAnswer } from '@/lib/rag/generateAnswer';
import chatClient from '@/lib/sqlite/chatClient';
import type { StoredUser } from '@/types/auth';
import type { ChatItem } from '@/types/chat';
import { getRecentPairs, extractJsonFromString } from './services';

import styles from './styles';

export default function HomeScreen() {
  const { user } = useAuth();
  const insects = useSafeAreaInsets();
  const router = useRouter();
  const params = useLocalSearchParams();
  const [messages, setMessages] = useState<ChatItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [animatingMessage, setAnimatingMessage] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const flatListRef = useRef<FlatList<any>>(null);
  const [currentConversationId, setCurrentConversationId] = useState<
    number | null
  >(null);
  const [copyFeedbackId, setCopyFeedbackId] = useState<string | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);

  useEffect(() => {
    setInitialLoading(true);
    try {
      const { conversationId, messages } =
        chatClient.getOrCreateFirstConversationWithMessages();
      setCurrentConversationId(conversationId);
      setMessages(
        messages.map((m: any) => ({
          id: m.id.toString(),
          sender: m.sender,
          text: m.text,
        }))
      );
    } catch (e) {
      // Optionally set an error state here
      console.error('Error initializing conversation:', e);
    } finally {
      setInitialLoading(false);
    }
  }, []);

  useEffect(() => {
    if (!initialLoading && params.isNewChat === '1') {
      handleNewChat();
      router.setParams({ isNewChat: undefined });
    }
  }, [params.isNewChat, initialLoading]);

  const scrollToBottom = () => {
    flatListRef.current?.scrollToOffset({ offset: 999999, animated: true });
  };

  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages]);

  const handleNewChat = () => {
    try {
      const newId = chatClient.createConversation('');
      if (!newId) throw new Error('Failed to create conversation');
      setCurrentConversationId(newId);
      setMessages([]);
    } catch (e) {
      console.error('Error creating new conversation (handleNewChat):', e);
    }
  };

  const addUserMessage = (input: string) => {
    const userMessage = {
      id: Date.now().toString(),
      sender: 'user',
      text: input,
    };
    setMessages((prev) => [...prev, userMessage]);
    if (currentConversationId != null) {
      chatClient.addMessage(currentConversationId, 'user', input);
    }
    return userMessage;
  };

  const addAIMessage = (text: string) => {
    setMessages((prev) => [
      ...prev,
      {
        id: (Date.now() + 1).toString(),
        sender: 'ai',
        text,
      },
    ]);
    if (currentConversationId != null) {
      chatClient.addMessage(currentConversationId, 'ai', text);
    }
  };

  const animateAIMessage = (aiText: string, words: string[]) => {
    setAnimatingMessage('');
    setIsTyping(true);
    let current = '';
    let i = 0;
    const revealNextWord = () => {
      if (i < words.length) {
        current = current ? current + ' ' + words[i] : words[i];
        setAnimatingMessage(current);
        i++;
        setTimeout(revealNextWord, 40);
      } else {
        addAIMessage(aiText);
        setAnimatingMessage(null);
        setIsTyping(false);
      }
    };
    revealNextWord();
  };

  const handleAIResponse = async (userMessage: any) => {
    setLoading(true);
    scrollToBottom();
    try {
      const recentPairs = getRecentPairs(messages, 5);
      const data = await generateAnswer(
        user as StoredUser,
        userMessage.text,
        '',
        recentPairs
      );
      let aiText = 'Sorry, I could not generate a response.';
      let appointment = false;
      if (data.response?.LLM) {
        const parsed = extractJsonFromString(data.response.LLM);
        if (parsed && typeof parsed.response === 'string') {
          aiText = parsed.response;
          appointment = !!parsed.appointment;
        }
      }
      const words = aiText.split(' ');
      if (words.length > 30) {
        animateAIMessage(aiText, words);
      } else {
        addAIMessage(aiText);
      }
      // Optionally handle appointment flag here (e.g., show UI)
    } catch (error) {
      addAIMessage('Sorry, something went wrong. Please try again.');
      console.error('Error generating AI answer:', error);
    } finally {
      setLoading(false);
      scrollToBottom();
    }
  };

  const handleSend = async (messageText: string) => {
    if (!messageText.trim() || !currentConversationId) {
      console.warn('No input or currentConversationId is null:', {
        messageText,
        currentConversationId,
      });
      return;
    }
    const userMessage = addUserMessage(messageText);
    await handleAIResponse(userMessage);
  };

  const handleCopy = async (text: string, id: string) => {
    await Clipboard.setStringAsync(text);
    setCopyFeedbackId(id);
    setTimeout(() => setCopyFeedbackId(null), 1200);
  };

  const Component = Platform.OS == 'android' ? View : KeyboardAvoidingView;
  const isIOS = Platform.OS === 'ios';

  if (initialLoading) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Loader />
      </View>
    );
  }

  return (
    <Component
      style={{ flex: 1 }}
      behavior={isIOS ? 'padding' : undefined}
      keyboardVerticalOffset={isIOS ? -insects.bottom : undefined}
    >
      <SafeAreaView edges={['top']} style={GLOBAL_STYLES.container}>
        <Header
          onNewChatPress={handleNewChat}
          newChatDisabled={messages.length === 0}
        />
        <View style={[GLOBAL_STYLES.content__gray, { flex: 1 }]}>
          {messages.length == 0 && (
            <>
              <Image
                source={require('@/assets/images/logo-green--no-text.png')}
                style={styles.logoImage}
              />
              <TextTypes
                type='h3'
                color={COLOURS.primary}
                customStyle={styles.welcomeMsg}
              >
                Welcome {user?.firstName}, how could I assist you today?
              </TextTypes>
            </>
          )}
          <ChatInterface
            messages={messages}
            onSend={handleSend}
            loading={loading}
            animatingMessage={animatingMessage}
            isTyping={isTyping}
            copyFeedbackId={copyFeedbackId}
            handleCopy={handleCopy}
            insects={insects}
            COLOURS={COLOURS}
            flatListRef={flatListRef}
            scrollToBottom={scrollToBottom}
          />
        </View>
      </SafeAreaView>
    </Component>
  );
}
