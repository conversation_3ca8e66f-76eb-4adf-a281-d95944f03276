import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLOURS.white,
  },
  settingStyle: {
    paddingTop: 2,
    textAlign: 'left'
  },
  mainContainer: {
    paddingHorizontal: 24,
  },
  sectionHeader: {
    marginTop: 30,
    marginBottom: 6,
    textAlign: 'left'
  },
  divider: {
    height: 14,
    backgroundColor: 'transparent',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: COLOURS.primary,
    borderRadius: 24,
    paddingVertical: 14,
    marginTop: 24,
    marginBottom: 12,
    backgroundColor: 'transparent',
  },
  logoutButtonText: {
    marginLeft: 4,
    textTransform: 'uppercase'
  },
});

export default styles; 