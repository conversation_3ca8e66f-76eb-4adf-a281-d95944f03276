import {  NativeModules } from 'react-native';
import { I18nManager, Platform, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import i18n from '@/lib/vet247/i18n';

import {
  BillingIcon,
  CupIcon,
  ForwordIcon,
  HelpIcon,
  InfoIcon,
  LanguageIcon,
  LockIcon,
  NotificationIcon,
  ProfileIcon,
  RedirectIcon,
  UpgradeIcon,
} from '@/components/icons';

import type { SettingsMenuItem } from '@/types/settings';

export function exitApp() {
  NativeModules.ExitAppModule.exitApp();
}

export const rtlLanguages = ['ar', 'he', 'fa', 'ur'];

export const accountSettingsItems: SettingsMenuItem[] = [
  {
    key: 'profile',
    icon: ProfileIcon,
    label: 'profile_information',
    rightIcon: ForwordIcon,
  },
  {
    key: 'upgrade',
    icon: UpgradeIcon,
    label: 'upgrade',
    badge: {
      icon: CupIcon,
      label: 'ultimate',
    },
    rightIcon: ForwordIcon,
  },
  {
    key: 'billing',
    icon: BillingIcon,
    label: 'billing',
    rightIcon: ForwordIcon,
  },
  {
    key: 'notifications',
    icon: NotificationIcon,
    label: 'notifications',
    rightIcon: ForwordIcon,
  },
  {
    key: 'language',
    icon: LanguageIcon,
    label: 'app_language',
    rightText: 'english_uk',
    rightIcon: ForwordIcon,
  },
];

export const aboutSettingsItems: SettingsMenuItem[] = [
  {
    key: 'about',
    icon: InfoIcon,
    label: 'about_us',
    rightIcon: RedirectIcon,
  },
  {
    key: 'help',
    icon: HelpIcon,
    label: 'help_center',
    rightIcon: RedirectIcon,
  },
  {
    key: 'terms',
    icon: LanguageIcon,
    label: 'terms_of_use',
    rightIcon: RedirectIcon,
  },
  {
    key: 'privacy',
    icon: LockIcon,
    label: 'privacy_policy',
    rightIcon: RedirectIcon,
  },
];

export async function changeAppLanguage(
  langValue: string,
  t?: (key: string) => string,
  exitAppFn?: () => void
) {
  const isRTL = rtlLanguages.includes(langValue.split('-')[0]);
  if (I18nManager.isRTL !== isRTL && Platform.OS !== 'web') {
    I18nManager.allowRTL(isRTL);
    I18nManager.forceRTL(isRTL);
    await i18n.changeLanguage(langValue);
    await AsyncStorage.setItem('appLanguage', langValue);

    Alert.alert(
      t ? t('restart_required_title') : 'Restart Required',
      t ? t('restart_required_message') : 'You need to restart the app for this language change.',
      [
        {
          text: 'OK',
          onPress: () => {
            if (exitAppFn) {
              exitAppFn();
            } else {
              exitApp();
            }
          },
        },
      ],
      { cancelable: false }
    );
    return;
  }
  await i18n.changeLanguage(langValue);
  await AsyncStorage.setItem('appLanguage', langValue);
}
