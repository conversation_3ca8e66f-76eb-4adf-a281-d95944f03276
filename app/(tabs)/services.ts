import type { ChatItem } from '@/types/chat';


export function getRecentPairs(messages: ChatItem[], maxPairs: number = 5): { qu: string; an: string }[] {
  const recentPairs: { qu: string; an: string }[] = [];
  let lastUser: ChatItem | null = null;
  for (let i = messages.length - 1; i >= 0 && recentPairs.length < maxPairs; i--) {
    const msg = messages[i];
    if (msg.sender === 'ai' && lastUser) {
      recentPairs.unshift({ qu: lastUser.text, an: msg.text });
      lastUser = null;
    } else if (msg.sender === 'user') {
      lastUser = msg;
    }
  }
  if (lastUser && recentPairs.length < maxPairs) {
    recentPairs.unshift({ qu: lastUser.text, an: '' });
  }
  return recentPairs;
}

export function extractJsonFromString(str: string): any | null {
  if (!str) return null;
  const match = str.match(/\{[\s\S]*?\}/);
  if (!match) return null;
  try {
    return JSON.parse(match[0]);
  } catch (e) {
    return null;
  }
}