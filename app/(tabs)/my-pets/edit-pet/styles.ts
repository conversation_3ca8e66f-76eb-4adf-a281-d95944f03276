import { StyleSheet } from 'react-native';

import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  mainContainer: { flex: 1, backgroundColor: COLOURS.grey },
  container: {
    flex: 1,
    backgroundColor: COLOURS.white,
  },
  contentContainer: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  petImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginVertical: 20,
    backgroundColor: COLOURS.lightGray,
    alignSelf: 'center',
    borderWidth: 1.5,
    borderColor: COLOURS.borderColor,
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: COLOURS.borderColor,
    borderRadius: 26,
    marginBottom: 16,
    marginTop: 20,
    alignSelf: 'center',
    padding: 3,
  },
  tab: {
    paddingVertical: 10,
    paddingHorizontal: 3,
    borderRadius: 26,
    flex: 1,
    alignItems: 'center',
  },
  tabActive: {
    backgroundColor: COLOURS.white,
    shadowColor: COLOURS.black,
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 8,
    backgroundColor: COLOURS.white,
  },
  cancelButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: COLOURS.primary,
    borderRadius: 30,
    paddingVertical: 12,
    marginRight: 10,
    alignItems: 'center',
    backgroundColor: COLOURS.white,
  },
  saveButton: {
    flex: 1,
    backgroundColor: COLOURS.primary,
    borderRadius: 30,
    paddingVertical: 12,
    marginLeft: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    letterSpacing: 0.8,
    textTransform: 'uppercase',
    width: '100%',
    textAlign: 'center',
  },
  saveButtonText: {
    letterSpacing: 0.8,
    textTransform: 'uppercase',
    width: '100%',
    textAlign: 'center',
  },
  loaderStyle: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default styles;
