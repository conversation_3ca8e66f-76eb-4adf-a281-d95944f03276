import { useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import Header from '@/components/header';
import TextTypes from '@/components/text-types';
import Loader from '@/components/ui/Loader';
import Button from '@/components/ui/button';
import PetItem from '@/components/ui/pet-item';
import type { PetItemProps } from '@/components/ui/pet-item/services';
import { COLOURS } from '@/constants/colours';

import { fetchPets } from './services';
import styles from './styles';
import { useIsFocused } from '@react-navigation/native';

export default function MyPetsScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const {
    container,
    content,
    buttonContainer,
    subtitle,
    button,
    buttonTextStyle,
    loaderStyle
  } = styles;

  const [pets, setPets] = useState<PetItemProps[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const isFocused = useIsFocused();

  useEffect(() => {
    const loadPets = async () => {
      if (pets.length == 0) {
        setLoading(true);
      }
      setError(null);
      try {
        const petsData = await fetchPets();
        setPets(petsData);
      } catch (err: any) {
        setError(err.message || 'Failed to load pets');
      } finally {
        setLoading(false);
      }
    };
    loadPets();
  }, [isFocused]);

  const onNewChat = () => {
    router.push({ pathname: '/(tabs)', params: { isNewChat: '1' } });
  };

  const onAddPet = () => {};

  const onPetItemClick = (id: number) => {
    router.push({
      pathname: '/my-pets/pet-details',
      params: { id: String(id) },
    });
  };

  return (
    <SafeAreaView style={container}>
      <Header title={t('drawer_my_pets')} onNewChatPress={onNewChat} />
      <View style={content}>
        <TextTypes type='h1' color={COLOURS.primary}>
          {t('drawer_my_pets')}
        </TextTypes>
        <TextTypes
          type='body3'
          color={COLOURS.secondaryTint}
          customStyle={subtitle}
        >
          {t('my_pets_subtitle')}
        </TextTypes>
        {loading ? (
          <View style={loaderStyle}>
            <Loader />
          </View>
        ) : error ? (
          <TextTypes type='body3' color='red'>
            {error}
          </TextTypes>
        ) : (
          <FlatList
            data={pets}
            keyExtractor={(item, idx) =>
              item.id ? String(item.id) : item.name + item.breed + idx
            }
            renderItem={({ item }) => (
              <PetItem
                image={item.image}
                name={item.name}
                breed={item.breed}
                sex={item.sex}
                id={item.id}
                age={item.age}
                weight={item.weight}
                onPress={onPetItemClick}
              />
            )}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 16 }}
          />
        )}
      </View>
      <View style={buttonContainer}>
        <Button
          style={button}
          textStyle={buttonTextStyle}
          title='Add Pet (£3/MO)'
          onPress={onAddPet}
        />
      </View>
    </SafeAreaView>
  );
}
