import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLOURS.white,
  },
  subtitle: {
    marginTop: 5,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 14,
    backgroundColor: COLOURS.grey,
  },
  buttonContainer: {
    paddingTop: 14,
    paddingHorizontal: 20,
    paddingBottom: 10,
    backgroundColor: COLOURS.white,
  },
  button: {
    marginTop: 0,
    padding: 14,
  },
  buttonTextStyle: {
    width: '100%',
    textAlign: 'center',
    textTransform: 'uppercase'
  },
  loaderStyle: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  }
});

export default styles;
