import { getPetDetails as fetchPetDetailsApi } from '@/lib/vet/getPetDetails';
import { getS3SignedImageUrl } from '@/lib/vet247/documents';
import { PetDetailsData, PetDetailsProps } from '@/pet';

export enum TabType {
  DETAILS = 'details',
  HEALTH = 'health'
}

export async function fetchPetDetails(petId: string | number): Promise<PetDetailsProps> {
  const petData = await fetchPetDetailsApi(petId);
  return mapPetDetailsToPetDetailsProps(petData);
}

export async function mapPetDetailsToPetDetailsProps(pet: PetDetailsData): Promise<PetDetailsProps> {
  let image: any = require('@/assets/images/logo-green--no-text.png');
  if (pet.petPicture) {
    try {
      image = { uri: await getS3SignedImageUrl(pet.petPicture) };
    } catch (e) {
      image = require('@/assets/images/icon.png');
    }
  }

  return {
    id: pet.id,
    name: pet.name,
    breed: pet.breed,
    species: pet.species,
    gender: pet.gender,
    weight: pet.weight ? `${pet.weight} ${pet.weightType || ''}`.trim() : '--',
    dateOfBirth: pet.dateOfBirth ? formatDateOfBirth(pet.dateOfBirth) : '--',
    microchipNumber: pet.microchipNumber || '--',
    insuranceProvider: pet.insuranceProvider || '--',
    insurancePolicyNumber: pet.insurancePolicyNumber || '--',
    image,
  };
}

function formatDateOfBirth(dob: string): string {
  if (!dob) return '--';
  const date = new Date(dob);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
}
