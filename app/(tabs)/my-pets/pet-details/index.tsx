import { useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { Image, ScrollView, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import Header from '@/components/header';
import TextTypes from '@/components/text-types';
import Loader from '@/components/ui/Loader';
import PetDetailsComponent from '@/components/ui/pet-details';
import PetHealthRecordsComponent from '@/components/ui/pet-health-records';
import { COLOURS } from '@/constants/colours';

import { fetchPetDetails, TabType } from './services';
import styles from './styles';
import { useIsFocused } from '@react-navigation/native';
import { PetDetailsProps } from '@/pet';

const PetDetails = () => {
  const router = useRouter();
  const { id } = useLocalSearchParams();

  const [pet, setPet] = useState<PetDetailsProps | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>(TabType.DETAILS);
  const isFocused = useIsFocused();

  const {
    container,
    contentContainer,
    petImage,
    petName,
    petBreed,
    tabs,
    tab,
    tabActive,
    buttonContainer,
    outlineButton,
    outlineButtonText,
    filledButton,
    filledButtonText,
    loaderStyle
  } = styles;

  useEffect(() => {
    const loadPetDetails = async () => {
      if (!id) return;
      if (!pet) {
        setLoading(true);
      }
      setError(null);
      try {
        const petData = await fetchPetDetails(id as string);
        setPet(petData);
      } catch (err: any) {
        setError(err.message || 'Failed to load pet details');
      } finally {
        setLoading(false);
      }
    };

    loadPetDetails();
  }, [id, isFocused]);

  const onNewChat = () => {
    // TODO: Implement new chat logic
  };

  const onEdit = () => {
    router.push(`/(tabs)/my-pets/edit-pet?id=${id}`);
  };

  if (loading) {
    return (
      <SafeAreaView style={container}>
        <Header title='' isBack onBackPress={() => router.back()} />
        <View style={loaderStyle}>
          <Loader />
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={container}>
        <Header title='' isBack onBackPress={() => router.back()} />
        <View style={contentContainer}>
          <TextTypes type='body3' color='red'>
            {error}
          </TextTypes>
        </View>
      </SafeAreaView>
    );
  }

  if (!pet) {
    return (
      <SafeAreaView style={container}>
        <Header title='' isBack onBackPress={() => router.back()} />
        <View style={contentContainer}>
          <TextTypes type='body3' color={COLOURS.secondaryTint}>
            Pet not found
          </TextTypes>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={container}>
      <Header
        title=''
        isBack
        onBackPress={() => router.replace('/(tabs)/my-pets')}
      />
      <ScrollView
        contentContainerStyle={contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <Image source={pet.image} style={petImage} />
        <TextTypes type='h1' color={COLOURS.primary} customStyle={petName}>
          {pet.name} details
        </TextTypes>
        <TextTypes
          type='h4'
          color={COLOURS.secondaryTint}
          customStyle={petBreed}
        >
          {pet.breed}
        </TextTypes>
        <View style={tabs}>
          <TouchableOpacity
            style={[tab, activeTab === TabType.DETAILS && tabActive]}
            onPress={() => setActiveTab(TabType.DETAILS)}
          >
            <TextTypes type='h5' color={COLOURS.textBlack}>
              Details
            </TextTypes>
          </TouchableOpacity>
          <TouchableOpacity
            style={[tab, activeTab === TabType.HEALTH && tabActive]}
            onPress={() => setActiveTab(TabType.HEALTH)}
          >
            <TextTypes type='h5' color={COLOURS.textBlack}>
              Health records
            </TextTypes>
          </TouchableOpacity>
        </View>
        {/* Tab Content */}
        {activeTab === TabType.DETAILS ? (
          <PetDetailsComponent pet={pet} />
        ) : (
          <PetHealthRecordsComponent pet={pet} />
        )}
      </ScrollView>
      <View style={buttonContainer}>
        <TouchableOpacity style={outlineButton} onPress={onNewChat}>
          <TextTypes
            type='buttonText'
            color={COLOURS.primary}
            customStyle={outlineButtonText}
          >
            New Chat
          </TextTypes>
        </TouchableOpacity>
        <TouchableOpacity style={filledButton} onPress={onEdit}>
          <TextTypes
            type='buttonText'
            color={COLOURS.white}
            customStyle={filledButtonText}
          >
            Edit
          </TextTypes>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default PetDetails;
