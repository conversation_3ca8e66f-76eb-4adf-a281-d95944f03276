import { StyleSheet } from 'react-native';

import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  drawerStyle: {
    width: '90%',
    borderTopRightRadius: 30,
    borderBottomRightRadius: 30,
    backgroundColor: COLOURS.white
  },
  copyText: {
    marginLeft: 5,
    alignSelf: 'center',
  },
  messageContainer: {
    borderRadius: 16,
    marginVertical: 4,
    padding: 12,
  },
  youText: {
    marginBottom: 5,
  },
  iconContainer: { flexDirection: 'row', justifyContent: 'flex-end' },
  footerLoaderContainer: {
    alignSelf: 'center',
    marginVertical: 4,
    marginLeft: 0,
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  footerContainer: {
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
    borderRadius: 16,
    marginVertical: 4,
    padding: 12,
  },
  welcomeMsg: {
    marginTop: 10,
    textAlign: 'center',
    marginHorizontal: 20,
    lineHeight: 26,
  },
  markDownTextStyle: { color: COLOURS.textBlack, fontSize: 16 },
  loaderContainer: { alignSelf: 'center', marginTop: 8 },
  listStyle: { padding: 16, paddingBottom: 6 },
  messageText: {
    marginTop: 6,
  },
  rowButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  inputBarWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 8,
  },
  bottomView: {
    backgroundColor: COLOURS.white,
    paddingHorizontal: 20,
    paddingTop: 15,
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
    borderTopEndRadius: 25,
    borderTopStartRadius: 25,
  },
  sendIcon: {
    marginLeft: 8,
  },
  chatView: {
    flex: 1,
  },
  inputBar: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: COLOURS.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLOURS.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    minHeight: 56,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    maxHeight: 100,
    paddingHorizontal: 8,
    paddingVertical: 12,
    color: COLOURS.textBlack,
  },
  logoImage: {
    width: 80,
    height: 80,
    alignSelf: 'center',
    resizeMode: 'contain',
    marginTop: 30,
  },
});

export default styles;
