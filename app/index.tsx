import { Redirect } from 'expo-router';
import { ActivityIndicator, View } from 'react-native';

import { GLOBAL_STYLES } from '@/constants/globalStyles';
import { useAuth } from '@/context/auth';

export default function Index() {
  const { isLoading, isLoggedIn } = useAuth();

  if (isLoading) {
    return (
      <View style={GLOBAL_STYLES.loaderContainer}>
        <ActivityIndicator size='large' />
      </View>
    );
  }

  if (isLoggedIn) {
    return <Redirect href="/(tabs)" />;
  }


  return <Redirect href={'/welcome'} />;
}
