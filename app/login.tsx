import { Redirect, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import LoginForm from '@/components/forms/login-form';
import Header from '@/components/header';
import SectionTitle from '@/components/section-title';
import { GLOBAL_STYLES } from '@/constants/globalStyles';
import { useAuth } from '@/context/auth';

const Login = () => {
  const { isLoading, isLoggedIn } = useAuth();
  const router = useRouter();

  const onBackPress = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <View style={GLOBAL_STYLES.loaderContainer}>
        <ActivityIndicator size='large' />
      </View>
    );
  }

  if (isLoggedIn) {
    return <Redirect href='/(tabs)' />;
  }

  return (
    <SafeAreaView edges={['top']} style={GLOBAL_STYLES.container}>
      <Header noTitle onBackPress={onBackPress} />
      <View style={GLOBAL_STYLES.content}>
        <SectionTitle
          title='Login'
          subtitle='Welcome back to you AI Vet Assistant!'
        />
        <LoginForm />
      </View>
    </SafeAreaView>
  );
};

export default Login;
