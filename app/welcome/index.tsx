import { useRouter } from 'expo-router';
import { Image, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { COLOURS } from '@/constants/colours';
import { Language } from '@/components/icons';
import TextTypes from '@/components/text-types';
import styles from './styles';

export default function Welcome() {
  const router = useRouter();
  const { container, languageRow, centerContent, logo, bottomContainer, registerButton, loginButton, languageText, welcomeSubtitle } = styles;

  return (
    <SafeAreaView style={container} edges={['top']}>
      <View style={languageRow}>
        <Language color={COLOURS.primary} />
        <TextTypes type='h4' color={COLOURS.primary} customStyle={languageText}>
          EN
        </TextTypes>
      </View>
      <View style={centerContent}>
        <Image
          source={require('../../assets/images/logo-green--no-text.png')}
          style={logo}
          resizeMode='contain'
        />
        <TextTypes type='h2' color={COLOURS.primary} customStyle={welcomeSubtitle}>
          Your smart assistant{"\n"}for better vet care
        </TextTypes>
      </View>
      <View style={bottomContainer}>
        <TouchableOpacity
          style={registerButton}
          onPress={() => router.push('/register')}
        >
          <TextTypes type='buttonText' color={COLOURS.primary}>
            REGISTER NEW ACCOUNT
          </TextTypes>
        </TouchableOpacity>
        <TouchableOpacity
          style={loginButton}
          onPress={() => router.push('/login')}
        >
          <TextTypes type='buttonText' color={COLOURS.white}>
            LOGIN
          </TextTypes>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
} 