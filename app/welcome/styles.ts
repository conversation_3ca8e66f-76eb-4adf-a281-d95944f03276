import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLOURS.white,
    justifyContent: 'space-between',
  },
  languageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    padding: 20,
    paddingTop: 10
  },
  centerContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  logo: {
    width: 118,
    height: 114,
    marginBottom: 28,
  },
  bottomContainer: {
    backgroundColor: COLOURS.greenText,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    paddingHorizontal: 24,
    paddingVertical: '13%',
    alignItems: 'center',
  },
  registerButton: {
    backgroundColor: COLOURS.white,
    borderRadius: 32,
    paddingVertical: 13,
    paddingHorizontal: 32,
    marginBottom: 16,
    marginVertical: 5,
    width: '100%',
    alignItems: 'center',
  },
  loginButton: {
    borderColor: COLOURS.white,
    borderWidth: 1,
    borderRadius: 32,
    paddingVertical: 13,
    marginVertical: 5,
    marginTop: 8,
    paddingHorizontal: 32,
    width: '100%',
    alignItems: 'center',
  },
  languageText: {
    marginLeft: 4,
  },
  welcomeSubtitle: {
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 16,
  },
});

export default styles; 