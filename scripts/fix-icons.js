const fs = require('fs');
const path = require('path');

const iconsDir = path.join(__dirname, '../components/icons');

// Get all .js files in the icons directory (excluding index.js)
const iconFiles = fs
  .readdirSync(iconsDir)
  .filter((file) => file.endsWith('.js') && file !== 'index.js');

function updateIconFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');

  // Extract the component name from the file content
  const componentMatch = content.match(/const Svg(\w+)/);
  if (!componentMatch) {
    console.log(`Skipping ${filePath}: Could not find component name`);
    return;
  }

  const componentName = componentMatch[1];

  // Create the updated content with viewBox and proper prop handling
  const updatedContent = `import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const Svg${componentName} = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='${content.match(/d='([^']+)'/)?.[1] || ''}'
    />
  </Svg>
);
export default Svg${componentName};
`;

  fs.writeFileSync(filePath, updatedContent);
  console.log(`Updated ${path.basename(filePath)}`);
}

// Update each icon file
iconFiles.forEach((file) => {
  const filePath = path.join(iconsDir, file);
  updateIconFile(filePath);
});

console.log(`\nUpdated ${iconFiles.length} icon files successfully!`);
