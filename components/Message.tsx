import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import Markdown from 'react-native-markdown-display';
import SvgCopy from './icons/MessageCopy';
import SvgLike from './icons/MessageLike';
import SvgUnlike from './icons/Unlike';
import TextTypes from './text-types';
import { COLOURS } from '@/constants/colours';
import styles from '@/app/(tabs)/styles';

interface MessageProps {
  item: {
    id: string;
    sender: string;
    text: string;
  };
  copyFeedbackId: string | null;
  handleCopy: (text: string, id: string) => void;
}

const Message: React.FC<MessageProps> = ({ item, copyFeedbackId, handleCopy }) => (
  <View
    style={[
      {
        alignSelf: item.sender === 'user' ? 'flex-end' : 'flex-start',
        backgroundColor:
          item.sender === 'user' ? COLOURS.orange : 'transparent',
        width: item.sender === 'user' ? undefined : '100%',
      },
      styles.messageContainer,
      item.sender === 'user'
        ? { borderTopRightRadius: 0 }
        : { borderBottomLeftRadius: 0, paddingHorizontal: 4 },
    ]}
  >
    {item.sender === 'user' && (
      <TextTypes customStyle={styles.youText} type='errorText' color={COLOURS.youText}>
        You
      </TextTypes>
    )}
    {item.sender === 'ai' ? (
      <>
        <Markdown style={{ body: styles.markDownTextStyle }}>
          {item.text}
        </Markdown>
        <View style={styles.iconContainer}>
          <TouchableOpacity onPress={() => handleCopy(item.text, item.id)}>
            <SvgCopy width={24} height={24} style={{ marginLeft: 8 }} />
          </TouchableOpacity>
          {copyFeedbackId === item.id && (
            <TextTypes customStyle={styles.copyText} color={COLOURS.primary} type={'body2'}>Copied!</TextTypes>
          )}
          <SvgLike width={24} height={24} style={{ marginLeft: 8 }} />
          <SvgUnlike width={24} height={24} style={{ marginLeft: 8 }} />
        </View>
      </>
    ) : (
      <TextTypes
        style={styles.messageText}
        type={'body2'}
        color={COLOURS.textBlack}
      >
        {item.text}
      </TextTypes>
    )}
  </View>
);

export default Message; 