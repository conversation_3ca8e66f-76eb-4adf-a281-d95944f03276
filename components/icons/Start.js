import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgStart = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M9.13 3.458a3.42 3.42 0 0 0-5.13 3v11.12a3.42 3.42 0 0 0 5.13 2.96l9.66-5.54a3.42 3.42 0 0 0 0-5.92zm-1 15.35a1.44 1.44 0 0 1-1.42 0 1.42 1.42 0 0 1-.71-1.23V6.418a1.42 1.42 0 0 1 .71-1.23 1.5 1.5 0 0 1 .71-.19c.249.005.492.07.71.19l9.66 5.58a1.42 1.42 0 0 1 0 2.46z'
    />
  </Svg>
);
export default SvgStart;
