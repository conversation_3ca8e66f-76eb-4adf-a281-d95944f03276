import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgDrop = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M12.47 1.803a1.125 1.125 0 0 0-1.29 0A24.3 24.3 0 0 0 7.226 5.46C4.592 8.484 3.2 11.69 3.2 14.725a8.625 8.625 0 0 0 17.25 0c0-7.253-7.654-12.694-7.98-12.922M11.825 21.1a6.38 6.38 0 0 1-6.375-6.375c0-3.123 1.875-5.941 3.44-7.754a23.4 23.4 0 0 1 2.935-2.83 23.4 23.4 0 0 1 2.934 2.83c1.566 1.813 3.441 4.631 3.441 7.754a6.38 6.38 0 0 1-6.375 6.375'
    />
  </Svg>
);
export default SvgDrop;
