import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgFood = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M12 13.299c1.554 0 2.822 1.247 2.823 2.794 0 .31-.058.605-.21.837a.96.96 0 0 1-.709.43c-.2.024-.397-.004-.61-.058l-.016-.004-.005-.002h-.006l-.007-.003-.062-.017h-.005l-.105-.032h-.003l-.337-.103-.225-.061a2 2 0 0 0-.522-.072c-.22 0-.446.04-.807.15h.001l-.277.085-.003.002-.071.02-.005.002-.065.018h-.003l-.06.017h-.004a1.7 1.7 0 0 1-.608.058.96.96 0 0 1-.71-.43c-.152-.232-.21-.526-.21-.837 0-1.547 1.268-2.794 2.822-2.794m-1.505 3.749a2 2 0 0 1-.128.02zq.068-.012.14-.03zm1.307-.325c-.134.013-.273.04-.434.08l.228-.05c.143-.027.273-.04.405-.04zm1.008-.703q.147.037.311.087a5 5 0 0 0-.591-.149zm-2.475.243-.035.006zl.025-.007zm-.059-.043-.003-.044zm-.006-.127v.012l.002.018-.002-.018zq0-.087.01-.172zM12 14.691c-.726 0-1.317.525-1.418 1.197l.286-.084.002-.001.3-.08c.289-.07.548-.108.83-.108.399 0 .754.073 1.206.21l.18.054.033.01a1.425 1.425 0 0 0-1.418-1.198m-1.652.899a2 2 0 0 0-.042.162za2 2 0 0 1 .058-.156zm.428-.696q-.059.058-.11.12zq.06-.057.123-.11zm2.56.12-.111-.12-.02-.017q.07.066.13.138m-1.16-.609q.09.01.173.026zM9.872 12.094a.829.829 0 0 1 0 1.66.829.829 0 0 1 0-1.66m-.5 1.014q.001.007.005.013a1 1 0 0 1-.027-.09zm1.033-.184-.01-.095v-.004q.01.05.01.1M14.129 12.094a.829.829 0 0 1 0 1.66.829.829 0 0 1 0-1.66m-.5 1.014.008.02a.5.5 0 0 1-.03-.096zm1.033-.184-.01-.095v-.004q.01.05.01.1M12 11.56c.458 0 .827.373.827.83s-.369.829-.826.829a.83.83 0 0 1-.827-.83c0-.456.369-.83.827-.83m-.5 1.013.009.02a.5.5 0 0 1-.03-.096zm1.033-.184-.01-.095v-.003q.01.048.01.098M12 6.346a.688.688 0 0 1 0 1.378c-.865 0-1.703.037-2.477.108v.001l-.22.02v.001c-.572.059-1.104.135-1.58.227l-.459.096c-.588.136-1.03.292-1.307.444l-.035.02.034.02.24.116c.268.114.627.227 1.068.33.585.134 1.277.244 2.04.321l.22.021c.774.072 1.611.11 2.477.11s1.702-.039 2.476-.11l.22-.02.559-.065c.543-.07 1.042-.157 1.48-.258L17.15 9c.385-.109.687-.224.894-.338l.035-.022a3 3 0 0 0-.335-.159l-.227-.084c-.604-.208-1.456-.385-2.468-.504l-.207-.023-.206-.021a.69.69 0 0 1-.618-.753v-.002l.028-.136a.69.69 0 0 1 .584-.482h.14l.167.017h.002l.163.018.161.019h.003l.159.019h.002l.156.02h.002l.154.022h.003l.15.023.149.022.003.001.145.023.001.001.143.023.01.002.054.01.012.001.139.026.003.001.136.027.034.007.033.006.001.001.131.027.007.001.127.03h.002l.126.029.035.008.021.005.011.003.12.03.008.002.045.012.015.004.116.033.115.033.005.001.11.034.056.017.003.002.106.034v.001l.063.021.096.035h.003l.32.131c.305.137.565.293.765.475.27.246.444.55.444.91 0 .604-.513 1.04-1.16 1.344-.671.316-1.61.553-2.716.711l-.198.028-.004.001q-.405.053-.839.093h-.002l-.215.02h-.003l-.22.017-.226.015h.001l-.226.015h-.003l-.228.013h-.008l-.106.005h-.006l-.232.01q-.357.014-.72.02h-.245v.001l-.248.001h-.25l-.241-.003h-.003l-.72-.019-.232-.01h-.005l-.108-.005h-.007l-.229-.013h-.003l-.226-.014-.224-.016-.22-.017H9.53l-.215-.02h-.002a24 24 0 0 1-.839-.093H8.47l-.198-.029c-1.106-.158-2.046-.395-2.717-.71-.647-.305-1.158-.74-1.159-1.346 0-.605.512-1.042 1.16-1.346.67-.316 1.61-.554 2.716-.712l.198-.028h.003l.413-.05q.21-.024.426-.043h.002l.215-.02h.004l.22-.017.45-.03h.003l.23-.013.006-.001.053-.002h.059l.231-.012h.002l.72-.02h.003l.242-.001h.003l.12-.001zm-.245 4.303h.49l.243-.003-.242.002-.245.002zm-1.98-.079q.11.008.221.015zm4.451-.001q-.11.01-.221.016zm-5.914-.155.198.027q.201.027.408.05l-.408-.05zq-.305-.045-.59-.096zm7.376-.001-.197.028-.408.05.408-.05zq.204-.03.398-.062zm2.233-1.374c-.027.012-.056.02-.084.032q.196-.077.346-.157zm1.394-.398a1 1 0 0 1-.003.079l.004-.078M4.698 8.496a1 1 0 0 0-.014.146l.004.078a.7.7 0 0 0 .013-.235zm12.912-.371c.34.117.597.24.76.362l.083.071q.034.034.052.06l.009.014.005.01-.008.02q.008-.014.009-.02l-.006-.01-.008-.015a.6.6 0 0 0-.136-.13c-.162-.122-.42-.246-.759-.363q-.068-.022-.139-.043.07.021.138.044M7.199 7.897a10 10 0 0 0-.376.095zq.225-.052.469-.1zm4.802-.462q-.329 0-.65.008zm2.317-.163.002.004a.4.4 0 0 1-.015-.072zm-1.95-.084a.4.4 0 0 0 .024-.072l.002-.022a.4.4 0 0 1-.025.094m-4.816-.192-.086.017q.216-.043.445-.082z'
    />
  </Svg>
);
export default SvgFood;
