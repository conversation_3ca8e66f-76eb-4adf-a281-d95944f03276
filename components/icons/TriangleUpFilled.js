import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgTriangleUpFilled = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M4.177 14.94c-.849.97-.16 2.489 1.129 2.489h13.388c1.29 0 1.978-1.518 1.13-2.488l-6.695-7.65a1.5 1.5 0 0 0-2.258 0z'
    />
  </Svg>
);
export default SvgTriangleUpFilled;
