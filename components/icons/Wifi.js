import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgWifi = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M12 15a3 3 0 1 0 0 6.001A3 3 0 0 0 12 15m0 4a1 1 0 1 1 0-2 1 1 0 0 1 0 2m0-8a7.06 7.06 0 0 0-4.95 2.05 1 1 0 0 0 0 1.41 1 1 0 0 0 1.41 0 5 5 0 0 1 7.08 0 1 1 0 0 0 .7.3 1 1 0 0 0 .76-1.71A7.06 7.06 0 0 0 12 11m0-4a11.08 11.08 0 0 0-7.78 3.22 1.005 1.005 0 0 0 1.42 1.42 9 9 0 0 1 12.72 0 1 1 0 0 0 .71.29.998.998 0 0 0 .71-1.71A11.08 11.08 0 0 0 12 7m10.61.39a15 15 0 0 0-21.22 0 1.004 1.004 0 0 0 1.42 1.42 13 13 0 0 1 18.38 0 1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42'
    />
  </Svg>
);
export default SvgWifi;
