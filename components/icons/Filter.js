import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgFilter = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M14.99 2.93c-1.267 0-2.45.832-2.818 2.002L3.99 4.93a1 1 0 0 0 0 2l8.18-.001c.449 1.225 1.552 2 2.82 2s2.382-.777 2.838-2.008l2.162.009a1 1 0 0 0 0-2h-2.17c-.482-1.22-1.562-2-2.83-2m0 2a1 1 0 1 1 0 2 1 1 0 0 1 0-2m-6 4c-1.317 0-2.42.82-2.823 2.002-.14.009-2.176-.002-2.176-.002a1 1 0 0 0 0 2s2.052-.021 2.18-.008a2.95 2.95 0 0 0 2.82 2.008c1.268 0 2.354-.777 2.83-1.997l8.17-.003a1 1 0 0 0 0-2l-8.176-.009A3.01 3.01 0 0 0 8.99 8.93m0 2a1 1 0 1 1 0 2 1 1 0 0 1 0-2m6 4c-1.267 0-2.427.835-2.819 1.99l-8.18.01a1 1 0 0 0 0 2h8.165a3.02 3.02 0 0 0 2.835 2c1.268 0 2.36-.793 2.842-2h2.158a1 1 0 0 0 0-2l-2.167-.006a3.03 3.03 0 0 0-2.833-1.994m0 2a1 1 0 1 1 0 2 1 1 0 0 1 0-2'
    />
  </Svg>
);
export default SvgFilter;
