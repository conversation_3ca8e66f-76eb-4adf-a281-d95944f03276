import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgArrowRight = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M13.29 6.29a1 1 0 0 0 0 1.42l3.3 3.29H5a1 1 0 0 0 0 2h11.59l-3.3 3.29a1.004 1.004 0 1 0 1.42 1.42l5-5q.137-.144.21-.33a.94.94 0 0 0 0-.76 1 1 0 0 0-.21-.33l-5-5a1 1 0 0 0-1.42 0'
    />
  </Svg>
);
export default SvgArrowRight;
