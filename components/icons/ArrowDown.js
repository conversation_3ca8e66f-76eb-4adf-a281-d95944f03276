import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgArrowDown = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M17.71 13.29a1 1 0 0 0-1.42 0L13 16.59V5a1 1 0 1 0-2 0v11.59l-3.29-3.3a1.004 1.004 0 1 0-1.42 1.42l5 5q.144.137.33.21a.94.94 0 0 0 .76 0 1 1 0 0 0 .33-.21l5-5a1 1 0 0 0 0-1.42'
    />
  </Svg>
);
export default SvgArrowDown;
