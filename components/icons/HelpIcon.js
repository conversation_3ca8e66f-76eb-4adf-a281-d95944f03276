import * as React from "react"
import Svg, { Path } from "react-native-svg"

function HelpIcon(props) {
  return (
    <Svg
      width={20}
      height={21}
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        d="M6.667 8.833A3.333 3.333 0 0110 5.5h.079a3.255 3.255 0 013.255 3.255v.427a2.662 2.662 0 01-1.82 2.525.995.995 0 00-.68.944V13a.833.833 0 01-1.667 0v-.35c0-1.145.733-2.162 1.82-2.524a.995.995 0 00.68-.944v-.427c0-.877-.711-1.588-1.588-1.588H10c-.92 0-1.666.746-1.666 1.666a.833.833 0 11-1.667 0zM10.834 15.5a.833.833 0 11-1.667 0 .833.833 0 011.667 0z"
        fill="#160F29"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 2.167a8.333 8.333 0 100 16.666 8.333 8.333 0 000-16.666zM3.334 10.5a6.667 6.667 0 1113.333 0 6.667 6.667 0 01-13.333 0z"
        fill="#160F29"
      />
    </Svg>
  )
}

export default HelpIcon
