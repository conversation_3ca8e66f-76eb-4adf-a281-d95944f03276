import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgTriangleDownFilled = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M11.371 16.71a1.5 1.5 0 0 0 2.258 0l7.194-8.222c.849-.97.16-2.488-1.129-2.488H5.306c-1.29 0-1.978 1.518-1.13 2.488z'
    />
  </Svg>
);
export default SvgTriangleDownFilled;
