import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgVideo = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M13.5 5a3 3 0 0 1 3 3v.759l3.744-2.185.176-.088c.181-.076.377-.115.575-.116l.196.013q.196.025.38.1l.176.086.164.11q.156.12.277.276l.11.164.088.177a1.5 1.5 0 0 1 .114.574v8.196l-.014.203a1.5 1.5 0 0 1-1.557 1.296 1.5 1.5 0 0 1-.76-.25L16.5 14.868V16a3 3 0 0 1-3 3l-9 .066a3 3 0 0 1-3-3v-8a3 3 0 0 1 3-3zm-9 2.066a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1l9-.066a1 1 0 0 0 1-1V8a1 1 0 0 0-1-1zm12 4.008v1.39l4 2.668V8.74z'
    />
  </Svg>
);
export default SvgVideo;
