import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgAddVideo = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M21.03 9.516a1 1 0 0 1 .5.134 1 1 0 0 1 .47.85v8a1 1 0 0 1-1 1 1 1 0 0 1-.44-.11L17 17.61c-.029.777-.357 2.012-.917 2.55A3 3 0 0 1 14 21H5a3 3 0 0 1-3-3v-7.986c0-.796.233-1.559.795-2.121a3 3 0 0 1 2.122-.879H12v2H4.917a1 1 0 0 0-.707.293c-.188.188-.21.442-.21.707V18a1 1 0 0 0 1 1h9a1 1 0 0 0 1-1v-7a1 1 0 0 1 1-1c1.016 0 1.003.798 1.004.637L17 11.39l3.53-1.74a1 1 0 0 1 .5-.134M17 15.38l3 1.5v-4.76l-3 1.5z'
    />
  </Svg>
);
export default SvgAddVideo;
