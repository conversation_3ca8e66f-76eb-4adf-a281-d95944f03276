import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgInfo = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M12 11a1 1 0 0 0-1 1v4a1 1 0 0 0 2 0v-4a1 1 0 0 0-1-1m.38-3.92a1 1 0 0 0-.76 0 1 1 0 0 0-.33.21 1.2 1.2 0 0 0-.21.33 1 1 0 0 0 .21 1.09q.147.133.33.21A1 1 0 0 0 13 8a1.05 1.05 0 0 0-.29-.71 1 1 0 0 0-.33-.21M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20m0 18a8 8 0 1 1 0-16.001A8 8 0 0 1 12 20'
    />
  </Svg>
);
export default SvgInfo;
