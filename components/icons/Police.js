import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgPolice = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='m8.648 15.265.427-2.52-1.851-1.796a.71.71 0 0 1-.182-.732.73.73 0 0 1 .585-.486l2.567-.379 1.164-2.288c.248-.486 1.056-.486 1.304 0l1.163 2.288 2.568.379c.272.04.5.228.585.486a.71.71 0 0 1-.182.732l-1.85 1.796.425 2.52a.71.71 0 0 1-.29.697.74.74 0 0 1-.764.057l-2.307-1.177-2.307 1.177a.74.74 0 0 1-.764-.057.71.71 0 0 1-.29-.697m2.68-4.94a.73.73 0 0 1-.543.389l-1.501.221 1.083 1.05a.71.71 0 0 1 .207.629l-.249 1.473c.074-.028.24-.117.44-.225.447-.24 1.07-.573 1.245-.543.18-.029.794.302 1.24.54.201.109.368.198.445.228l-.25-1.473a.71.71 0 0 1 .208-.628l1.083-1.05-1.501-.222a.73.73 0 0 1-.544-.388l-.681-1.34z'
    />
  </Svg>
);
export default SvgPolice;
