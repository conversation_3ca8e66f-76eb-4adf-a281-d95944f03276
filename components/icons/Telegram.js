import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgTelegram = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M20.399 4.168a1.68 1.68 0 0 0-1.7-.265L4.034 9.823a1.671 1.671 0 0 0 .086 3.124l2.966 1.032 1.653 5.467a.8.8 0 0 0 .097.206q.01.014.022.027a.8.8 0 0 0 .23.217q.118.077.254.111l.01.001.005.002q.082.017.166.017l.015-.003a.8.8 0 0 0 .247-.042q.026-.012.052-.025a.8.8 0 0 0 .167-.094l.124-.104 2.211-2.441 3.298 2.554c.29.226.648.349 1.016.35a1.68 1.68 0 0 0 1.642-1.337l2.67-13.104a1.66 1.66 0 0 0-.567-1.613M9.849 14.24a.8.8 0 0 0-.224.414l-.253 1.23-.641-2.121 3.326-1.732zm6.792 4.34-3.897-3.02a.82.82 0 0 0-1.107.098l-.709.782.25-1.216 5.796-5.796a.818.818 0 0 0-.957-1.304L7.7 12.453 4.653 11.34l14.71-5.885z'
    />
  </Svg>
);
export default SvgTelegram;
