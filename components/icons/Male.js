import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgMale = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M19.875 3h-4.5c-.619 0-1.125.506-1.125 1.125s.506 1.125 1.125 1.125h1.777l-4.466 4.466a6.1 6.1 0 0 0-3.498-1.091A6.184 6.184 0 0 0 3 14.813 6.184 6.184 0 0 0 9.188 21a6.184 6.184 0 0 0 6.187-6.187 6.2 6.2 0 0 0-1.091-3.51l4.466-4.456v1.778c0 .619.506 1.125 1.125 1.125S21 9.244 21 8.625v-4.5C21 3.506 20.494 3 19.875 3M9.188 18.75a3.94 3.94 0 0 1-3.938-3.937 3.94 3.94 0 0 1 3.938-3.938 3.94 3.94 0 0 1 3.937 3.938 3.94 3.94 0 0 1-3.937 3.937'
    />
  </Svg>
);
export default SvgMale;
