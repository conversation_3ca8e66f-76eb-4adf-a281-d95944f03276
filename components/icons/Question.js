import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgQuestion = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M8 10a4 4 0 0 1 4-4h.094A3.906 3.906 0 0 1 16 9.906v.513c0 1.374-.88 2.595-2.184 3.03A1.19 1.19 0 0 0 13 14.58V15a1 1 0 1 1-2 0v-.419c0-1.374.88-2.595 2.184-3.03.487-.162.816-.618.816-1.132v-.513A1.906 1.906 0 0 0 12.094 8H12a2 2 0 0 0-2 2 1 1 0 1 1-2 0M13 18a1 1 0 1 1-2 0 1 1 0 0 1 2 0'
    />
  </Svg>
);
export default SvgQuestion;
