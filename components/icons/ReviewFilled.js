import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgReviewFilled = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M17 22q.081 0 .159-.013a4.5 4.5 0 1 1 .841-8.96V8.94a1.3 1.3 0 0 0-.06-.27v-.09a1 1 0 0 0-.19-.28l-6-6c-.184-.173-.451-.27-.7-.3H5a3 3 0 0 0-3 3v14a3 3 0 0 0 3 3zM5.293 8.293A1 1 0 0 1 6 8h1a1 1 0 0 1 0 2H6a1 1 0 0 1-.707-1.707m0 4A1 1 0 0 1 6 12h6a1 1 0 0 1 0 2H6a1 1 0 0 1-.707-1.707M10 18a1 1 0 0 0 0-2H6a1 1 0 0 0 0 2zm4.59-10L12 5.41V7a1 1 0 0 0 1 1zm5.95 11.13 1.17 1.16a1.002 1.002 0 0 1-.326 1.639 1 1 0 0 1-1.094-.219l-1.16-1.17A3.46 3.46 0 0 1 14 17.49 3.49 3.49 0 0 1 20 15a3.44 3.44 0 0 1 .54 4.13m-3.055-.172c.392 0 .77-.15 1.055-.418a1.5 1.5 0 0 0 .46-1.05A1.47 1.47 0 0 0 17.44 16a1.48 1.48 0 0 0-1 .43 1.46 1.46 0 0 0-.44 1.06c0 .393.155.77.43 1.05a1.54 1.54 0 0 0 1.055.418'
    />
  </Svg>
);
export default SvgReviewFilled;
