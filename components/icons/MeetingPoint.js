import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgMeetingPoint = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='m13.13 7.868-.002-2.802.711.72a.904.904 0 0 0 1.279-1.278l-2.251-2.251a.9.9 0 0 0-.298-.19.9.9 0 0 0-.684 0 .9.9 0 0 0-.297.19l-2.251 2.25a.9.9 0 0 0 0 1.28.9.9 0 0 0 1.279 0l.71-.721.003 2.802a.9.9 0 0 0 1.801 0M7.845 11.02l-2.802.003.72-.71a.904.904 0 0 0-1.279-1.28l-2.25 2.252a.9.9 0 0 0-.19.297.9.9 0 0 0 0 .684.9.9 0 0 0 .19.297l2.25 2.251a.9.9 0 0 0 1.279 0 .9.9 0 0 0 0-1.278l-.72-.712 2.802-.002a.9.9 0 0 0 0-1.801'
    />
  </Svg>
);
export default SvgMeetingPoint;
