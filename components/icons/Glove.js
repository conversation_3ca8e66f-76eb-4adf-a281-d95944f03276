import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgGlove = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M12.97 1a2.264 2.264 0 0 0-2.233 1.853 2.4 2.4 0 0 0-.486-.053c-1.272 0-2.25 1.045-2.25 2.268v4.338l-.267-.31a2.39 2.39 0 0 0-2.608-.779c-1.42.46-2.08 2.11-1.283 3.407l1.41 2.339c1.345 2.24 2.4 3.742 3.635 4.65q.384.281.783.484l.34 2.403a1 1 0 0 0 .996.9h5.19a1 1 0 0 0 .995-.9l.261-2.62q.511-.27.96-.624c1.368-1.079 2.189-2.694 2.189-4.756V7.75c0-1.222-.96-2.25-2.233-2.25-.137 0-.297.014-.468.048V5.05c0-1.222-.96-2.25-2.232-2.25-.146 0-.318.016-.501.056A2.22 2.22 0 0 0 12.97 1m-1.758 17.433c-.069-.365-.34-.65-.673-.814a4 4 0 0 1-.585-.357c-.908-.667-1.801-1.865-3.157-4.125L5.382 10.79l-.005-.008c-.164-.266-.051-.639.304-.754h.002a.594.594 0 0 1 .676.228l1.86 2.16a.9.9 0 0 0 1.582-.603V5.068a.458.458 0 0 1 .607-.443.5.5 0 0 1 .163.075.26.26 0 0 1 .083.094.55.55 0 0 1 .047.256V9.1a.9.9 0 1 0 1.8 0V3.25a.46.46 0 0 1 .469-.45c.237 0 .431.182.431.45V9.1a.9.9 0 1 0 1.8 0V5.05a.5.5 0 0 1 .048-.25.3.3 0 0 1 .087-.096.639.639 0 0 1 .334-.104c.238 0 .432.182.432.45V9.1a.9.9 0 1 0 1.8 0V7.75c0-.142.028-.217.047-.253a.26.26 0 0 1 .084-.095.64.64 0 0 1 .336-.102c.239 0 .433.182.433.45v5.85c0 1.538-.59 2.622-1.503 3.344a4.6 4.6 0 0 1-.867.535c-.359.173-.627.504-.66.901l-.156 1.936a.2.2 0 0 1-.199.184h-3.65a.2.2 0 0 1-.196-.163z'
    />
  </Svg>
);
export default SvgGlove;
