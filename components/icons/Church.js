import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgChurch = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M21.447 14.105 18 12.382V12a1 1 0 0 0-.485-.857L13 8.434V6h1a1 1 0 1 0 0-2h-1V3a1 1 0 1 0-2 0v1h-1a1 1 0 0 0 0 2h1v2.434l-4.515 2.709A1 1 0 0 0 6 12v.382l-3.447 1.724A1 1 0 0 0 2 15v6a1 1 0 0 0 1 1h18a1 1 0 0 0 1-1v-6c0-.379-.214-.725-.553-.895M4 15.927a.5.5 0 0 1 .276-.447l1-.5a.5.5 0 0 1 .724.447V19.5a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zM12 15a2 2 0 0 0-2 2v3H8.5a.5.5 0 0 1-.5-.5v-6.65a.5.5 0 0 1 .243-.43l3.5-2.1a.5.5 0 0 1 .514 0l3.5 2.1a.5.5 0 0 1 .243.43v6.65a.5.5 0 0 1-.5.5H14v-3a2 2 0 0 0-2-2m8 4.5a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-4.073a.5.5 0 0 1 .724-.447l1 .5a.5.5 0 0 1 .276.447z'
    />
  </Svg>
);
export default SvgChurch;
