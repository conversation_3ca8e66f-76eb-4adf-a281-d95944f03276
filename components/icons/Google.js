import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgGoogle = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M21.637 10.186a.91.91 0 0 0-.894-.748h-8.561a.91.91 0 0 0-.909.91v3.515a.91.91 0 0 0 .909.91h3.601a3.3 3.3 0 0 1-1.028 1.079 4.6 4.6 0 0 1-2.573.713 4.49 4.49 0 0 1-4.223-3.125v-.001a4.46 4.46 0 0 1 0-2.879v-.001a4.49 4.49 0 0 1 4.223-3.124 3.98 3.98 0 0 1 2.846 1.107.91.91 0 0 0 1.271-.014l2.607-2.607a.907.907 0 0 0-.023-1.308 9.66 9.66 0 0 0-6.701-2.611 9.95 9.95 0 0 0-8.934 5.507v.002A9.9 9.9 0 0 0 2.183 12a10.1 10.1 0 0 0 1.063 4.489l.001.001a9.95 9.95 0 0 0 8.934 5.507 9.57 9.57 0 0 0 6.63-2.442h.001v-.001a9.8 9.8 0 0 0 3.004-7.348q0-1.019-.18-2.021M12.182 3.82a7.9 7.9 0 0 1 4.734 1.52l-1.321 1.321a5.87 5.87 0 0 0-3.413-1.044 6.26 6.26 0 0 0-5.444 3.228l-.972-.754-.531-.412a8.14 8.14 0 0 1 6.947-3.86M4.437 14.639a8.2 8.2 0 0 1 0-5.278l1.62 1.256a6.2 6.2 0 0 0 0 2.765zm7.745 5.541a8.14 8.14 0 0 1-6.948-3.859l.344-.267 1.16-.9a6.26 6.26 0 0 0 5.444 3.229 6.6 6.6 0 0 0 3.005-.682l1.537 1.193a8.1 8.1 0 0 1-4.542 1.287m5.931-2.509-.166-.129-1.252-.972a5.1 5.1 0 0 0 1.273-2.54.908.908 0 0 0-.894-1.076H13.09v-1.698h6.861q.047.472.047.95a8.23 8.23 0 0 1-1.886 5.465'
    />
  </Svg>
);
export default SvgGoogle;
