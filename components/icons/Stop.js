import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgStop = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M15 10H9a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1m-1 5.6a.4.4 0 0 1-.4.4h-3.2a.4.4 0 0 1-.4-.4v-3.2c0-.22.18-.4.4-.4h3.2c.22 0 .4.18.4.4zM12 4a10 10 0 1 0 0 20 10 10 0 0 0 0-20m0 18a8 8 0 1 1 0-16.001A8 8 0 0 1 12 22'
    />
  </Svg>
);
export default SvgStop;
