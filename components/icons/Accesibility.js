import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgAccesibility = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20m0 18a8 8 0 1 1 0-16.001A8 8 0 0 1 12 20m0-11a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3m4.25.75a.79.79 0 0 1-.5 1l-2.25.75V13l1.255 3.556a.834.834 0 0 1-1.572.558L12.25 14.5h-.5l-.934 2.614a.834.834 0 0 1-1.571-.558L10.5 13v-1.5l-2.25-.75a.79.79 0 0 1 .5-1.5L11 10h2l2.25-.75a.79.79 0 0 1 1 .5'
    />
  </Svg>
);
export default SvgAccesibility;
