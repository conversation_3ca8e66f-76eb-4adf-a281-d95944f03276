import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgTukTuk = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M20.924 12.801c.015.066.015.135.014 1.156v1.14A2.75 2.75 0 0 1 23 17.75a2.75 2.75 0 0 1-2.75 2.75c-1.279 0-2.344-.697-2.654-1.88H6.404c-.31 1.183-1.375 1.88-2.654 1.88-2.337 0-3.63-2.757-2.062-4.551V8.813A4.82 4.82 0 0 1 6.5 4h9.543a3.44 3.44 0 0 1 3.334 2.606l1.54 6.167.003.014zM6.5 5.375a3.44 3.44 0 0 0-3.437 3.438V12h1.259V8.812c0-1.134 0-2.312 1.678-2.312h6V5.375zm5.5 2.75v8.995h-1.974v-2.808C10.026 13.178 9.01 12 7.875 12H6V8.812c0-.378 0-.687.5-.687zm1.5 0h4.487L19 12h-5.5zm4-1.625h-4V5.375h2.543c.883 0 1.166.31 1.457 1.125m-11 10.62h2.062v-2.808a.69.69 0 0 0-.687-.687H3.062v1.471c1.561-.405 3.074.636 3.438 2.024m7 0h4.096c.255-.962 1.004-1.776 1.967-2.024v-1.471H13.5zm-8.5.75a1.25 1.25 0 1 1-2.5 0 1.25 1.25 0 0 1 2.5 0m15.25 1.25a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5'
    />
  </Svg>
);
export default SvgTukTuk;
