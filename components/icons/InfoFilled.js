import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgInfoFilled = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M6.444 3.685a10 10 0 1 1 11.112 16.63A10 10 0 0 1 6.444 3.685M12.38 7.08a1 1 0 0 0-.76 0 1 1 0 0 0-.33.21q-.133.147-.21.33A.84.84 0 0 0 11 8a1 1 0 0 0 .29.71q.147.133.33.21A1 1 0 0 0 13 8a1.05 1.05 0 0 0-.29-.71 1 1 0 0 0-.33-.21M12 11a1 1 0 0 0-1 1v4a1 1 0 0 0 2 0v-4a1 1 0 0 0-1-1'
    />
  </Svg>
);
export default SvgInfoFilled;
