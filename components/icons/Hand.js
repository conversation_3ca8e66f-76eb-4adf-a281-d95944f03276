import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgHand = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M12.418 2.1a2.264 2.264 0 0 0-2.233 1.853A2.4 2.4 0 0 0 9.7 3.9c-1.273 0-2.25 1.045-2.25 2.268v.887l-2.876 3.262c-1.42.46-2.08 2.11-1.283 3.407l1.41 2.34h.001c1.344 2.24 2.4 3.741 3.635 4.649 1.298.954 2.696 1.187 4.513 1.187 1.883 0 3.672-.486 5.012-1.544 1.367-1.079 2.188-2.694 2.188-4.756V8.85c0-1.222-.96-2.25-2.232-2.25-.138 0-.297.014-.468.048V6.15c0-1.222-.96-2.25-2.232-2.25-.146 0-.318.016-.502.056A2.22 2.22 0 0 0 12.418 2.1M7.45 7.055l-2.876 3.262a2.395 2.395 0 0 1 2.608.779l.268.31zm9.947 1.542a.54.54 0 0 0-.047.253v2.25a.9.9 0 0 1-1.8 0V6.15c0-.268-.194-.45-.432-.45a.64.64 0 0 0-.334.104.3.3 0 0 0-.087.096.5.5 0 0 0-.047.25v4.95a.9.9 0 0 1-1.8 0V4.35c0-.268-.194-.45-.432-.45a.46.46 0 0 0-.468.45v6.75a.9.9 0 0 1-1.8 0V6.15a.55.55 0 0 0-.048-.256.26.26 0 0 0-.083-.094.61.61 0 0 0-.32-.1c-.238 0-.449.2-.449.468v7.646a.9.9 0 0 1-1.582.603l-1.861-2.16-.025-.03a.59.59 0 0 0-.651-.198H5.13c-.356.115-.468.488-.305.754l.005.008 1.415 2.346c1.356 2.26 2.25 3.458 3.157 4.125.845.621 1.785.838 3.448.838 1.597 0 2.957-.414 3.897-1.156.914-.721 1.503-1.806 1.503-3.344V8.85c0-.268-.194-.45-.432-.45a.64.64 0 0 0-.336.102.26.26 0 0 0-.085.095'
    />
  </Svg>
);
export default SvgHand;
