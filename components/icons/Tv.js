import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgTv = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M10.056 6 8.613 3.5a1 1 0 1 1 1.732-1l1.634 2.83 1.634-2.83a1 1 0 1 1 1.732 1L13.9 6H18a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3v1a1 1 0 0 1-2 0v-1H8v1a1 1 0 1 1-2 0v-1a3 3 0 0 1-3-3V9a3 3 0 0 1 3-3zm8.651 11.707A1 1 0 0 0 19 17V9a1 1 0 0 0-1-1H6a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h12a1 1 0 0 0 .707-.293'
    />
  </Svg>
);
export default SvgTv;
