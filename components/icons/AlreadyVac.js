import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgAlreadyVac = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M21.213 2.77a.95.95 0 0 0-1.349 0l-2.668 2.641-.675-.665a.95.95 0 0 0-1.339 0l-.684.713-.674-.675a.95.95 0 0 0-1.348 0l-7.38 7.39-.703-.666a.95.95 0 0 0-1.31 1.34l3.352 3.361-1.643 1.653-.674-.684a.954.954 0 0 0-1.349 1.349l2.688 2.688a.95.95 0 0 0 .674.275.951.951 0 0 0 .675-1.624L6.13 19.2l1.653-1.652 2.324 2.323.035-.051c.32-.821.357-1.821.32-2.291l-4.07-4.175 1.32-1.368 2.754 2.825v-1.025a.816.816 0 0 1 .812-.816l-2.137-2.137 4.027-4.027 1.308 1.309 1.38-1.356 1.33 1.33-1.34 1.395 1.349 1.35-2.364 2.34a6.5 6.5 0 0 1 2.796-.097l1.572-1.57a.95.95 0 0 0 0-1.348l-.675-.675.684-.664a.95.95 0 0 0 0-1.34l-.665-.674 2.669-2.64a.95.95 0 0 0 0-1.396'
    />
  </Svg>
);
export default SvgAlreadyVac;
