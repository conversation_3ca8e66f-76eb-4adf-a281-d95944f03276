import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgPhoneCall = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M19.521 13.036c-.22 0-.452-.07-.672-.121a9.4 9.4 0 0 1-1.315-.393 2 2 0 0 0-2.489 1.007l-.22.453a12.2 12.2 0 0 1-2.67-2.013 12.3 12.3 0 0 1-2.008-2.678l.422-.281a2.018 2.018 0 0 0 1.004-2.496q-.24-.646-.392-1.32a8 8 0 0 1-.12-.684 3.02 3.02 0 0 0-1.044-1.81 3 3 0 0 0-1.967-.696H5.04A3 3 0 0 0 2.75 3.03a3.02 3.02 0 0 0-.722 2.407 19.15 19.15 0 0 0 5.458 11.135c3.002 3 6.915 4.912 11.122 5.433h.381A3.006 3.006 0 0 0 22 18.974v-3.02a3.03 3.03 0 0 0-.718-1.904 3 3 0 0 0-1.76-1.014m.502 6.039a1.01 1.01 0 0 1-.726.969 1.05 1.05 0 0 1-.438.037 17.04 17.04 0 0 1-9.923-4.901 17.13 17.13 0 0 1-4.84-9.975 1.1 1.1 0 0 1 .25-.825 1 1 0 0 1 .753-.343H8.11a1 1 0 0 1 1.004.796q.06.412.15.815.174.797.462 1.56l-1.405.654a1 1 0 0 0-.584.956q.008.201.092.383a14.56 14.56 0 0 0 7.025 7.046c.245.1.519.1.763 0a1 1 0 0 0 .572-.524l.622-1.409q.78.279 1.586.463.402.09.813.151a1.004 1.004 0 0 1 .793 1.006z'
    />
  </Svg>
);
export default SvgPhoneCall;
