import * as React from "react"
import Svg, { Rect, Path } from "react-native-svg"

function PetOwner(props) {
  return (
    <Svg
      width={53}
      height={52}
      viewBox="0 0 53 52"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Rect x={0.5} width={52} height={52} rx={26} fill="#D8F0F3" />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M37.48 27.931a2.701 2.701 0 013.214 1.1c.**************.018.03l.***************.001.004.138.256a2.863 2.863 0 01-.96 3.59l-.248.153-10.09 5.542c-.44.24-.937.37-1.44.37h-9.216a1.19 1.19 0 00-.836.342l-.013.013-.016.012-.787.644-.002-.001a.892.892 0 01-.468.225l-.188.01a.902.902 0 01-.65-.337l-.001-.001-3.909-4.906-.001.001a.9.9 0 01.09-1.22l.006-.006 4.297-3.81h.001c.037-.033.07-.059.085-.07l.01-.008.22.321-.218-.32c1.086-.745 2.498-.968 3.965-.966 1.471 0 3.062.23 4.535.44l.987.138c.963.13 1.85.229 2.612.229a2.475 2.475 0 012.216 1.368l6.409-3.049.235-.099zm-9.199 10.643a2.562 2.562 0 01.162-.014l-.162.014zm.938-.24a2.68 2.68 0 01-.149.064l.149-.065c.048-.023.098-.047.145-.073l-.145.073zm9.953-8.342a.9.9 0 00-.994-.396l-.158.059-7.049 3.354a2.481 2.481 0 01-.478.828l-.114.123c-.467.466-1.1.727-1.76.727h-4.967a.901.901 0 010-1.799h4.968a.691.691 0 00.487-.202l.084-.103a.684.684 0 00.117-.368v-.02l-.014-.134a.687.687 0 00-.103-.245l-.084-.102a.69.69 0 00-.487-.202c-.733 0-1.62-.104-2.565-.236l-.961-.14c-1.424-.208-2.919-.426-4.271-.452-1.355-.027-2.487.144-3.242.642l-3.617 3.194 2.81 3.527.064-.042a2.992 2.992 0 012.063-.825h9.215c.201-.001.4-.051.575-.147l10.09-5.542.093-.057c.207-.143.359-.357.424-.603l.022-.107a1.061 1.061 0 00-.138-.715l-.01-.017zm-15.934 3.516a.507.507 0 00.061-.073l-.06.073zm6.072-.468a1.103 1.103 0 01-.086.064l.086-.064c.027-.022.054-.046.08-.071l-.08.071zm1.33-.316c-.022.09-.05.178-.085.263l.086-.263zm-3.617-1.725c.07.008.14.012.207.02l-.4-.046.193.026zm13.706-.302a2.458 2.458 0 01-.026.247l.026-.247c.005-.082.006-.165.003-.246l-.003.246zm-1.208-.903a.135.135 0 00-.01-.013l.01.013zm-21.76-.103c-.043.015-.085.033-.127.049.114-.044.231-.085.35-.121l-.224.072zm21.428-.264l-.097-.065-.017-.01c.**************.114.075z"
        fill="#256B74"
      />
      <Path
        d="M23.7 18.561c.515 0 .933.417.933.933v.01a.932.932 0 01-1.866 0v-.01c0-.516.418-.933.934-.933zM29.3 18.561c.516 0 .933.417.933.933v.01a.932.932 0 01-1.866 0v-.01c0-.516.418-.933.934-.933z"
        fill="#256B74"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M31.254 11.37a2.8 2.8 0 013.137 1.984l1.382 4.774a1.826 1.826 0 01-.977 2.108c-.378 2.268-1.004 4.095-1.937 5.4-1.048 1.469-2.476 2.258-4.225 2.259h-4.267c-1.75 0-3.178-.79-4.226-2.258-.933-1.306-1.56-3.133-1.938-5.401A1.88 1.88 0 0117.84 20a1.824 1.824 0 01-.612-1.873l1.384-4.774a2.8 2.8 0 013.135-1.984l3.97.64.078.017h1.413a.959.959 0 01.078-.017l3.969-.64zm-10.82 8.435c-.107.118-.227.22-.357.307.355 2.029.9 3.485 1.582 4.44.729 1.021 1.613 1.475 2.708 1.475h1.2v-1.866c0-.065.006-.13.019-.19a1.618 1.618 0 01-.445-.317c-.393-.393-.508-.905-.508-1.36 0-.515.418-.933.933-.933h1.867c.516 0 .934.418.934.933 0 .455-.114.967-.506 1.36-.137.136-.289.24-.447.316.**************.02.192v1.865h1.2c1.095 0 1.978-.454 2.707-1.475.682-.954 1.228-2.41 1.583-4.439a1.862 1.862 0 01-.359-.31l-5.512-5.91h-1.106l-5.514 5.912zm1.014-6.593a.933.933 0 00-1.044.662l-1.357 4.68.014-.016 4.63-4.964-2.243-.362zm10.104 0l-2.244.362 4.63 4.964.016.017-1.356-4.681a.934.934 0 00-1.046-.662z"
        fill="#256B74"
      />
    </Svg>
  )
}

export default PetOwner
