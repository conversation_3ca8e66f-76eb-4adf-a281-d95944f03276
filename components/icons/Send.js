import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgSend = ({ width = 48, height = 48, color = '#B3B3B3', bgColor = '#EEEEEE', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 48 48'
    fill='none'
    {...props}
  >
    <Path d="M0 24C0 10.7452 10.7452 0 24 0C37.2548 0 48 10.7452 48 24C48 37.2548 37.2548 48 24 48C10.7452 48 0 37.2548 0 24Z" fill={bgColor}/>
    <Path d="M29.71 22.7102C29.617 22.804 29.5064 22.8784 29.3846 22.9291C29.2627 22.9799 29.132 23.006 29 23.006C28.868 23.006 28.7373 22.9799 28.6154 22.9291C28.4936 22.8784 28.383 22.804 28.29 22.7102L25 19.4102L25 31.0002C25 31.2655 24.8946 31.5198 24.7071 31.7074C24.5196 31.8949 24.2652 32.0002 24 32.0002C23.7348 32.0002 23.4804 31.8949 23.2929 31.7074C23.1054 31.5198 23 31.2655 23 31.0002L23 19.4102L19.71 22.7102C19.5217 22.8985 19.2663 23.0043 19 23.0043C18.7337 23.0043 18.4783 22.8985 18.29 22.7102C18.1017 22.5219 17.9959 22.2665 17.9959 22.0002C17.9959 21.7339 18.1017 21.4785 18.29 21.2902L23.29 16.2902C23.3851 16.1992 23.4973 16.1278 23.62 16.0802C23.7397 16.0273 23.8691 16 24 16C24.1309 16 24.2603 16.0273 24.38 16.0802C24.5028 16.1278 24.6149 16.1992 24.71 16.2902L29.71 21.2902C29.8037 21.3832 29.8781 21.4938 29.9289 21.6157C29.9797 21.7375 30.0058 21.8682 30.0058 22.0002C30.0058 22.1323 29.9797 22.263 29.9289 22.3848C29.8781 22.5067 29.8037 22.6173 29.71 22.7102Z" fill={color}/>
  </Svg>
);

export default SvgSend; 