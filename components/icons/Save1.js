import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgSave1 = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M5 21h14a2 2 0 0 0 2-2V8a1 1 0 0 0-.29-.71l-4-4A1 1 0 0 0 16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2m10-2H9v-4.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 .5.5zM13 6.5a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5V5h4zM5 6a1 1 0 0 1 1-1h1v3a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V5h.176a1 1 0 0 1 .707.293l2.824 2.824a1 1 0 0 1 .293.707V18a1 1 0 0 1-1 1h-1v-5a2 2 0 0 0-2-2H9a2 2 0 0 0-2 2v5H6a1 1 0 0 1-1-1z'
    />
  </Svg>
);
export default SvgSave1;
