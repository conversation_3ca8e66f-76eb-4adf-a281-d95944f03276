import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgEdit = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M19.239 1.94c-.749 0-1.467.297-1.996.826l-8.589 8.589a.9.9 0 0 0-.238.42l-.904 3.616a.904.904 0 0 0 1.097 1.097l3.616-.904a.9.9 0 0 0 .42-.238l8.59-8.59a2.822 2.822 0 0 0-1.996-4.817m-.717 2.105a1.014 1.014 0 0 1 1.434 1.433l-8.412 8.412-1.912.478.478-1.912z'
    />
  </Svg>
);
export default SvgEdit;
