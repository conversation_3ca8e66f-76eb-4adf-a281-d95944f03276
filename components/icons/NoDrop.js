import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgNoDrop = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M5.167 4.421a1.125 1.125 0 1 0-1.665 1.513l1.927 2.12c-1.47 2.3-2.22 4.609-2.22 6.874a8.625 8.625 0 0 0 14.344 6.459l.952 1.047a1.125 1.125 0 0 0 1.665-1.513zm6.667 16.882a6.38 6.38 0 0 1-6.375-6.375c0-1.664.52-3.387 1.546-5.14l9.03 9.938a6.36 6.36 0 0 1-4.201 1.577M8.516 5.875a1.125 1.125 0 0 1 .011-1.594 23 23 0 0 1 2.662-2.278 1.125 1.125 0 0 1 1.29 0c.326.23 7.98 5.672 7.98 12.925a9 9 0 0 1-.094 1.25 1.124 1.124 0 1 1-2.226-.323 6.5 6.5 0 0 0 .07-.927c0-4.963-4.64-9.18-6.375-10.583-.435.35-1.05.875-1.726 1.541a1.125 1.125 0 0 1-1.592-.01'
    />
  </Svg>
);
export default SvgNoDrop;
