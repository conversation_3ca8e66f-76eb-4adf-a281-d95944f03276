import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgExpand = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='m9.997 4.405-.79.8a1.004 1.004 0 1 1-1.42-1.42l2.5-2.5a1 1 0 0 1 .33-.21 1 1 0 0 1 .76 0 1 1 0 0 1 .33.21l2.5 2.5a1 1 0 0 1-.326 1.639 1 1 0 0 1-1.094-.219l-.79-.8v3.59a1 1 0 0 1-2 0zM11.997 17.585l.79-.8a1.004 1.004 0 1 1 1.42 1.42l-2.5 2.5a1 1 0 0 1-.33.21.94.94 0 0 1-.76 0 1 1 0 0 1-.33-.21l-2.5-2.5a1.004 1.004 0 1 1 1.42-1.42l.79.8v-3.59a1 1 0 1 1 2 0zM4.405 11.999l.8.79a1.004 1.004 0 1 1-1.42 1.42l-2.5-2.5a1 1 0 0 1-.21-.33 1 1 0 0 1 0-.76 1 1 0 0 1 .21-.33l2.5-2.5a1 1 0 0 1 1.42 0 1 1 0 0 1 0 1.42l-.8.79h3.592a1 1 0 1 1 0 2zM17.585 9.998l-.8-.79a1.004 1.004 0 1 1 1.42-1.42l2.5 2.5a1 1 0 0 1 .21.33.94.94 0 0 1 0 .76 1 1 0 0 1-.21.33l-2.5 2.5a1.003 1.003 0 1 1-1.42-1.42l.8-.79h-3.588a1 1 0 0 1 0-2z'
    />
  </Svg>
);
export default SvgExpand;
