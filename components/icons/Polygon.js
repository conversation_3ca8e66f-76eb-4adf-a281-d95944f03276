import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgPolygon = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M20 14.185v-2.369A3 3 0 0 0 22 9c0-1.654-1.346-3-3-3a3 3 0 0 0-2.116.876L12.969 5.31c.01-.103.031-.204.031-.31 0-1.654-1.346-3-3-3S7 3.346 7 5c0 .762.295 1.451.765 1.981L6.091 9.212A3 3 0 0 0 5 9c-1.654 0-3 1.346-3 3s1.346 3 3 3c.159 0 .313-.023.465-.047L7.4 17.532c-.248.436-.4.932-.4 1.468 0 1.654 1.346 3 3 3a2.99 2.99 0 0 0 2.863-2.153l3.962-.792A3 3 0 0 0 19 20c1.654 0 3-1.346 3-3a2.995 2.995 0 0 0-2-2.815M19 8a1.001 1.001 0 1 1 .002 2.002A1.001 1.001 0 0 1 19 8m-9-4a1.001 1.001 0 1 1 .002 2.002A1.001 1.001 0 0 1 10 4m-6 8a1.001 1.001 0 1 1 2.002-.002A1.001 1.001 0 0 1 4 12m6 8a1 1 0 1 1 0-2 1 1 0 0 1 0 2m2.761-2.172A3 3 0 0 0 10 16c-.386 0-.752.079-1.091.213l-1.674-2.231C7.705 13.451 8 12.762 8 12c0-.536-.152-1.032-.399-1.467l1.935-2.58c.152.024.305.047.464.047a3 3 0 0 0 2.116-.876l3.915 1.566c-.01.103-.031.204-.031.31 0 1.302.839 2.401 2 2.815v2.369a3 3 0 0 0-2 2.815c0 .061.015.117.018.177zM19 18a1 1 0 1 1 0-2 1 1 0 0 1 0 2'
    />
  </Svg>
);
export default SvgPolygon;
