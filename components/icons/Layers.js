import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgLayers = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='m2.505 10.064 9 5.2a1 1 0 0 0 1 0l9-5.2a1 1 0 0 0 0-1.73l-9-5.2a1 1 0 0 0-1 0l-9 5.2a1 1 0 0 0 0 1.73m9.5-4.91 7 4-7 4.05-7-4.01zm8.5 7.79-8.5 4.91-8.5-4.91a.999.999 0 1 0-1 1.73l9 5.2a1 1 0 0 0 1 0l9-5.2a1 1 0 0 0-.24-1.834 1 1 0 0 0-.76.104'
    />
  </Svg>
);
export default SvgLayers;
