import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgArrowUp = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M17.71 10.71a1 1 0 0 1-1.095.22 1 1 0 0 1-.325-.22L13 7.41V19a1 1 0 1 1-2 0V7.41l-3.29 3.3a1.004 1.004 0 0 1-1.42-1.42l5-5a1 1 0 0 1 .33-.21.94.94 0 0 1 .76 0 1 1 0 0 1 .33.21l5 5a1 1 0 0 1 0 1.42'
    />
  </Svg>
);
export default SvgArrowUp;
