import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgCommunity = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M12 5.545a3.727 3.727 0 0 0-2.785 6.205 5.55 5.55 0 0 0-2.76 4.795v.91a1 1 0 1 0 2 0v-.91a3.545 3.545 0 1 1 7.09 0v.91a1 1 0 1 0 2 0v-.91a5.55 5.55 0 0 0-2.76-4.795A3.727 3.727 0 0 0 12 5.545m-1.221 2.506a1.727 1.727 0 1 1 2.443 2.443 1.727 1.727 0 0 1-2.443-2.443'
    />
  </Svg>
);
export default SvgCommunity;
