import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgUserLocation = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M14.49 10.86a3.09 3.09 0 1 0-5 0c-.425.27-.803.608-1.12 1A1.03 1.03 0 1 0 10 13.12a2.62 2.62 0 0 1 2.05-1 2.62 2.62 0 0 1 2.05 1 1 1 0 0 0 1.56-1.25 4.7 4.7 0 0 0-1.17-1.01M12 10.13a1.09 1.09 0 1 1 .001-2.18A1.09 1.09 0 0 1 12 10.13m8.46-.5A8.5 8.5 0 0 0 7.3 3.36a8.56 8.56 0 0 0-3.76 6.27A8.46 8.46 0 0 0 6 16.46l5.3 5.31a1 1 0 0 0 1.42 0L18 16.46a8.46 8.46 0 0 0 2.46-6.83m-3.86 5.42-4.6 4.6-4.6-4.6a6.49 6.49 0 0 1-1.87-5.22A6.57 6.57 0 0 1 8.42 5a6.47 6.47 0 0 1 7.16 0 6.57 6.57 0 0 1 2.89 4.81 6.49 6.49 0 0 1-1.87 5.24'
    />
  </Svg>
);
export default SvgUserLocation;
