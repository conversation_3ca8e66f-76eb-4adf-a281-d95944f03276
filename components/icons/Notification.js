import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgNotification = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M6 17h12v-6.6C18 6.79 15.436 4 12 4s-6 2.789-6 6.4zm6-15c4.418 0 8 3.76 8 8.4V19H4v-8.6C4 5.76 7.582 2 12 2M9.778 19.674h4.444c0 .617-.234 1.209-.65 1.645A2.17 2.17 0 0 1 12 22c-.59 0-1.155-.245-1.571-.681a2.38 2.38 0 0 1-.651-1.645'
    />
  </Svg>
);
export default SvgNotification;
