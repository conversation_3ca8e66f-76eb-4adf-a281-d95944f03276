import * as React from "react"
import Svg, { Rect, Path } from "react-native-svg"

function TorchIcon(props) {
  return (
    <Svg
      width={60}
      height={60}
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Rect width={60} height={60} rx={30} fill="#256B74" />
      <Path
        d="M23.314 16.933v-.38c0-1.866.913-2.799 2.738-2.799h7.896c1.825 0 2.737.934 2.737 2.8v.38h-13.37zM28.76 45.44c-.895 0-1.575-.24-2.04-.718-.465-.478-.698-1.176-.698-2.092V27.26c0-.718-.075-1.326-.225-1.825a5.322 5.322 0 00-.605-1.354l-.77-1.18a11.164 11.164 0 01-.799-1.506 4.331 4.331 0 01-.308-1.662v-1.056h13.371v1.056a4.21 4.21 0 01-.317 1.662 9.812 9.812 0 01-.8 1.507l-.759 1.18a5.322 5.322 0 00-.605 1.353c-.15.499-.226 1.107-.226 1.825v15.37c0 .916-.235 1.614-.707 2.092-.465.479-1.142.718-2.03.718h-2.482zm-1.19-17.011v4.214c0 .684.233 1.258.698 1.723.472.465 1.053.697 1.743.697.451 0 .858-.106 1.22-.318a2.388 2.388 0 001.2-2.102v-4.214c0-.451-.11-.858-.328-1.22a2.321 2.321 0 00-.871-.872 2.315 2.315 0 00-1.22-.328c-.691 0-1.272.236-1.744.707-.465.465-.697 1.036-.697 1.713zm2.441 5.783c-.45 0-.827-.147-1.128-.441-.3-.3-.45-.677-.45-1.128 0-.43.15-.796.45-1.097a1.534 1.534 0 011.128-.462c.43 0 .8.154 1.108.462.307.3.461.666.461 1.097 0 .451-.15.827-.451 1.128-.3.294-.673.44-1.118.44z"
        fill="#fff"
      />
    </Svg>
  )
}

export default TorchIcon
