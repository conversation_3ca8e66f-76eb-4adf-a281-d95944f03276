import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgNewspaper = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M7 11h1a1 1 0 0 1 0 2H7a1 1 0 0 1 0-2m0 4h1a1 1 0 0 1 0 2H7a1 1 0 0 1 0-2m6-6H7a1 1 0 0 1 0-2h6a1 1 0 1 1 0 2M3 3h14a1 1 0 0 1 1 1v3h3a1 1 0 0 1 1 1v10a3 3 0 0 1-3 3H6a4 4 0 0 1-4-4V4a1 1 0 0 1 1-1m15 15a1 1 0 0 0 2 0V9.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5zM4 17a2 2 0 0 0 2 2h10.18a3 3 0 0 1-.18-1V5.5a.5.5 0 0 0-.5-.5h-11a.5.5 0 0 0-.5.5zm9-4h-1a1 1 0 0 1 0-2h1a1 1 0 0 1 0 2m0 4h-1a1 1 0 0 1 0-2h1a1 1 0 0 1 0 2'
    />
  </Svg>
);
export default SvgNewspaper;
