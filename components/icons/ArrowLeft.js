import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgArrowLeft = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M10.71 6.29a1 1 0 0 1 0 1.42L7.41 11H19a1 1 0 0 1 0 2H7.41l3.3 3.29a1.004 1.004 0 0 1-1.42 1.42l-5-5a1 1 0 0 1-.21-.33.94.94 0 0 1 0-.76 1 1 0 0 1 .21-.33l5-5a1 1 0 0 1 1.42 0'
    />
  </Svg>
);
export default SvgArrowLeft;
