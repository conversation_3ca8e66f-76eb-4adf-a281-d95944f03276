import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgShare = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M11.293 3.293a1 1 0 0 1 .324-.217l1.09.217 4 4a1 1 0 0 1-1.414 1.414L13 6.414V16a1 1 0 1 1-2 0V6.414L8.707 8.707a1 1 0 0 1-1.414-1.414zM11.617 3.076l1.088.215a1 1 0 0 0-1.088-.215'
    />
  </Svg>
);
export default SvgShare;
