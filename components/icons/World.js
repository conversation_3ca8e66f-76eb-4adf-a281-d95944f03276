import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgWorld = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10m5.535-15.776A8.003 8.003 0 0 0 4.523 9.149l1.05-.175a3 3 0 0 1 3.453 3.452l-.134.807a2.34 2.34 0 0 0 1.26 2.475 3.5 3.5 0 0 1 1.831 3.982l-.077.31H12a8 8 0 0 0 3.162-.65l.121-.362q.234-.703.317-1.44l.046-.417a5 5 0 0 0-1.803-4.422l-2-1.636A2.578 2.578 0 0 1 13.475 6.5h3.358a1 1 0 0 0 .6-.2zm.058 11.496.04-.368a7 7 0 0 0-2.524-6.191l-2-1.636a.578.578 0 0 1 .366-1.025h3.358a3 3 0 0 0 1.8-.6l.163-.122A7.96 7.96 0 0 1 20 12a7.98 7.98 0 0 1-2.407 5.72m-7.68 2.005.13-.52a1.5 1.5 0 0 0-.786-1.708 4.34 4.34 0 0 1-2.338-4.592l.134-.807a1 1 0 0 0-1.15-1.151l-1.87.311a8.003 8.003 0 0 0 5.879 8.467'
    />
  </Svg>
);
export default SvgWorld;
