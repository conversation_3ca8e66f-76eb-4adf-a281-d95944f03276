import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgExternalLink = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M18 11a1 1 0 0 0-1 1v7a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1h7a1 1 0 1 0 0-2H5a3 3 0 0 0-3 3v11a3 3 0 0 0 3 3h11a3 3 0 0 0 3-3v-7a1 1 0 0 0-1-1m3.42-7.56a1 1 0 0 0-.54-.54 1 1 0 0 0-.38-.08h-6a1 1 0 1 0 0 2h3.59l-9.8 9.47a1 1 0 0 0 .325 1.639 1 1 0 0 0 1.095-.219l9.79-9.48v3.59a1 1 0 0 0 2 0v-6a1 1 0 0 0-.08-.38'
    />
  </Svg>
);
export default SvgExternalLink;
