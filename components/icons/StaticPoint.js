import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgStaticPoint = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M11 11.9V17a1 1 0 0 0 2 0v-5.1a5 5 0 1 0-2 0M12 4a3 3 0 1 1 0 6 3 3 0 0 1 0-6m4.21 10.42a1.023 1.023 0 0 0-.42 2C18.06 16.87 19 17.68 19 18c0 .58-2.45 2-7 2s-7-1.42-7-2c0-.32.94-1.13 3.21-1.62a1.023 1.023 0 0 0-.42-2C4.75 15.08 3 16.39 3 18c0 2.63 4.53 4 9 4s9-1.37 9-4c0-1.61-1.75-2.92-4.79-3.58'
    />
  </Svg>
);
export default SvgStaticPoint;
