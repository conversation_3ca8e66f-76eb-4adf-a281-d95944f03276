import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgPath = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M6 16.268A2 2 0 0 1 5 20a2 2 0 0 1-1-3.732V8.5a4.5 4.5 0 0 1 9 0v7a2.5 2.5 0 0 0 5 0V8.83a3.001 3.001 0 1 1 2 0v6.67a4.5 4.5 0 1 1-9 0v-7a2.5 2.5 0 0 0-5 0zm13.707-9.56a1 1 0 1 1-1.414-1.415 1 1 0 0 1 1.414 1.414'
    />
  </Svg>
);
export default SvgPath;
