import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgAdd = ({ width = 40, height = 40, color = '#256B74', ...props }) => (
  <Svg
    width={width}
    height={height}
    viewBox="0 0 40 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Path
      d="M27 19H21V13C21 12.7348 20.8946 12.4804 20.7071 12.2929C20.5196 12.1054 20.2652 12 20 12C19.7348 12 19.4804 12.1054 19.2929 12.2929C19.1054 12.4804 19 12.7348 19 13V19H13C12.7348 19 12.4804 19.1054 12.2929 19.2929C12.1054 19.4804 12 19.7348 12 20C12 20.2652 12.1054 20.5196 12.2929 20.7071C12.4804 20.8946 12.7348 21 13 21H19V27C19 27.2652 19.1054 27.5196 19.2929 27.7071C19.4804 27.8946 19.7348 28 20 28C20.2652 28 20.5196 27.8946 20.7071 27.7071C20.8946 27.5196 21 27.2652 21 27V21H27C27.2652 21 27.5196 20.8946 27.7071 20.7071C27.8946 20.5196 28 20.2652 28 20C28 19.7348 27.8946 19.4804 27.7071 19.2929C27.5196 19.1054 27.2652 19 27 19Z"
      fill={color}
    />
  </Svg>
);

export default SvgAdd;
