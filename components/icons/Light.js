import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgLight = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M10.488 2.157a7.1 7.1 0 0 1 5.92 1.4 7 7 0 0 1 1 9.83 4.26 4.26 0 0 0-1.02 2.64v.43a2 2 0 0 1 .57 1.4v2a2 2 0 0 1-2 2h-6a2 2 0 0 1-2-2v-2a2 2 0 0 1 .61-1.4v-.29a4.76 4.76 0 0 0-1.08-2.86 7 7 0 0 1 4-11.15m-1.53 15.7v2h6v-2zm4.133-13.772a5 5 0 0 0-2.233-.008 5 5 0 0 0-2.84 8 6.67 6.67 0 0 1 1.5 3.78H11V13a1 1 0 0 1 2 0v2.857h1.388a6.18 6.18 0 0 1 1.46-3.72 4.92 4.92 0 0 0 1.11-3.14 5 5 0 0 0-3.867-4.912'
    />
  </Svg>
);
export default SvgLight;
