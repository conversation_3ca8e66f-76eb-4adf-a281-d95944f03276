import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgWiffiOff = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M6.92 5.51 3.71 2.29a1.004 1.004 0 1 0-1.42 1.42L4.56 6A15.2 15.2 0 0 0 1.4 8.39a1 1 0 0 0 0 1.41 1 1 0 0 0 .71.3 1 1 0 0 0 .7-.29 13 13 0 0 1 3.24-2.35L7.54 9a10.8 10.8 0 0 0-3.32 2.27 1 1 0 1 0 1.42 1.4 8.8 8.8 0 0 1 3.45-2.12l1.62 1.61a7.07 7.07 0 0 0-3.66 1.94 1 1 0 1 0 1.42 1.4A5 5 0 0 1 12 14q.317 0 .63.05l7.66 7.66a1 1 0 0 0 1.639-.325 1 1 0 0 0-.219-1.095zM12 16a3 3 0 1 0 0 6 3 3 0 0 0 0-6m0 4a1 1 0 1 1 0-2 1 1 0 0 1 0 2M22.61 8.39A15 15 0 0 0 10.29 4.1a1.006 1.006 0 1 0 .22 2A13.07 13.07 0 0 1 21.2 9.81a1 1 0 0 0 1.41-1.42m-4.25 4.24a1 1 0 0 0 1.42-1.4 10.75 10.75 0 0 0-4.84-2.82 1 1 0 1 0-.52 1.92 8.94 8.94 0 0 1 3.94 2.3'
    />
  </Svg>
);
export default SvgWiffiOff;
