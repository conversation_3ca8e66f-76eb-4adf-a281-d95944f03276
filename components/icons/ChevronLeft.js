import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgChevronLeft = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='m11.29 12 3.54-3.54a1 1 0 1 0-1.42-1.41l-4.24 4.24a1 1 0 0 0 0 1.42L13.41 17a1 1 0 0 0 .71.29 1 1 0 0 0 .71-.29 1 1 0 0 0 0-1.41z'
    />
  </Svg>
);
export default SvgChevronLeft;
