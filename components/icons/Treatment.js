import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgTreatment = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M11 11a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v2h2a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-2v2a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-2H9a.5.5 0 0 1-.5-.5v-1A.5.5 0 0 1 9 13h2z'
    />
  </Svg>
);
export default SvgTreatment;
