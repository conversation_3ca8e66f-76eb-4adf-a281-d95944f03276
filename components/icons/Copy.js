import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgCopy = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M20 2H10c-1.103 0-2 .897-2 2v4H4c-1.103 0-2 .897-2 2v10c0 1.103.897 2 2 2h10c1.103 0 2-.897 2-2v-4h4c1.103 0 2-.897 2-2V4c0-1.103-.897-2-2-2M5 20a1 1 0 0 1-1-1v-8a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1l.002 8a1 1 0 0 1-1 1zm15-7a1 1 0 0 1-1 1h-3v-4c0-1.103-.897-2-2-2h-4V5a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1z'
    />
  </Svg>
);
export default SvgCopy;
