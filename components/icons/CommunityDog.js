import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgCommunityDog = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M16.224 4.333a8.7 8.7 0 0 0-2.971-1.006.667.667 0 1 1 .188-1.32 10 10 0 0 1 3.554 1.228 2.66 2.66 0 0 1 1.74-.657 2.69 2.69 0 0 1 2.687 2.687 2.66 2.66 0 0 1-.657 1.74 10 10 0 0 1 1.228 3.554.665.665 0 0 1-.66.76.67.67 0 0 1-.66-.572 8.7 8.7 0 0 0-1.006-2.971 2.7 2.7 0 0 1-.932.176 2.69 2.69 0 0 1-2.687-2.687c.002-.319.062-.634.176-.932m3.263-.193a1.354 1.354 0 1 0-1.504 2.25 1.354 1.354 0 0 0 1.504-2.25M20.934 12.814a.668.668 0 0 1 1.06.627 10 10 0 0 1-1.229 3.553c.421.483.654 1.1.657 1.741a2.69 2.69 0 0 1-2.687 2.687 2.66 2.66 0 0 1-1.74-.657 10 10 0 0 1-3.554 1.228.66.66 0 0 1-.549-.172.666.666 0 0 1 .36-1.147 8.7 8.7 0 0 0 2.972-1.007 2.7 2.7 0 0 1-.176-.932 2.69 2.69 0 0 1 2.687-2.687c.319.002.634.062.932.176a8.7 8.7 0 0 0 1.007-2.971.67.67 0 0 1 .26-.439m-2.951 7.046a1.353 1.353 0 1 0 1.504-2.25 1.353 1.353 0 0 0-1.504 2.25M7.776 19.667a8.7 8.7 0 0 0 2.972 1.007.666.666 0 1 1-.19 1.32 10 10 0 0 1-3.552-1.229c-.483.421-1.1.654-1.741.657a2.69 2.69 0 0 1-2.687-2.687c.003-.64.236-1.258.657-1.74a10 10 0 0 1-1.228-3.554.667.667 0 1 1 1.32-.188 8.7 8.7 0 0 0 1.006 2.971c.297-.114.613-.174.932-.176a2.69 2.69 0 0 1 2.687 2.687 2.7 2.7 0 0 1-.176.932m-3.263.193a1.354 1.354 0 1 0 1.504-2.251 1.354 1.354 0 0 0-1.504 2.251M7.006 3.235a10 10 0 0 1 3.553-1.228.666.666 0 1 1 .189 1.32 8.7 8.7 0 0 0-2.972 1.006c.114.298.174.613.176.932a2.69 2.69 0 0 1-2.687 2.687 2.7 2.7 0 0 1-.932-.176 8.7 8.7 0 0 0-1.006 2.972.667.667 0 0 1-1.32-.19 10 10 0 0 1 1.228-3.552 2.66 2.66 0 0 1-.657-1.74 2.69 2.69 0 0 1 2.687-2.688c.64.003 1.258.236 1.74.657M4.513 6.391A1.354 1.354 0 1 0 6.017 4.14a1.354 1.354 0 0 0-1.504 2.25'
    />
  </Svg>
);
export default SvgCommunityDog;
