import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgSchool = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M12.15 3.061a.5.5 0 0 0-.479 0l-9.15 4.991a1 1 0 0 0 0 1.756l2.13 1.16a.5.5 0 0 1 .26.44v5.109a1 1 0 0 0 .52.878l6.24 3.405a.5.5 0 0 0 .48 0l6.5-3.547a.5.5 0 0 0 .26-.44v-5.406a.5.5 0 0 1 .26-.439l1-.545c.74-.493.74-.493.74.44v5.567a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5V9.228a.5.5 0 0 0-.26-.439zm5.776 5.43c.985.44.985.44 0 .878l-5.776 3.15a.5.5 0 0 1-.478 0L5.896 9.37c-.985-.439-.985-.439 0-.878l5.776-3.15a.5.5 0 0 1 .478 0zm-1.015 7.133a.5.5 0 0 1-.26.439l-4.5 2.457a.5.5 0 0 1-.48 0l-4.5-2.457a.5.5 0 0 1-.26-.44v-2.58a.5.5 0 0 1 .74-.439l4.02 2.196a.5.5 0 0 0 .48 0l4.02-2.196a.5.5 0 0 1 .74.44z'
    />
  </Svg>
);
export default SvgSchool;
