import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgOwned = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M20.235 13.448c.89-.325 1.899.01 2.41.825l.014.023.002.003h.001l.001.004.104.191a2.15 2.15 0 0 1-.72 2.693l-.187.115-7.567 4.156c-.33.18-.703.277-1.08.277H6.3a.9.9 0 0 0-.627.257l-.01.01-.012.009-.59.483H5.06a.67.67 0 0 1-.351.168l-.14.007a.68.68 0 0 1-.488-.252H4.08l-2.932-3.68a.675.675 0 0 1 .067-.915l.005-.005 3.222-2.857.001.001a1 1 0 0 1 .064-.053l.008-.007.165.242-.164-.24c.814-.559 1.873-.726 2.973-.725 1.104 0 2.297.172 3.402.33l.74.103c.722.098 1.387.172 1.959.172a1.857 1.857 0 0 1 1.662 1.026l4.807-2.286zm-6.9 7.983.122-.011zm.704-.181-.111.049zq.056-.025.11-.055zm7.465-6.256a.675.675 0 0 0-.745-.297l-.12.044-5.285 2.516a1.9 1.9 0 0 1-.359.621l-.086.092c-.35.35-.825.546-1.32.546H9.865a.676.676 0 0 1 0-1.35h3.726a.52.52 0 0 0 .365-.151l.063-.078a.5.5 0 0 0 .088-.276v-.015l-.01-.1a.5.5 0 0 0-.078-.184l-.063-.077a.52.52 0 0 0-.365-.151c-.55 0-1.216-.078-1.924-.177l-.72-.104c-1.068-.157-2.19-.32-3.204-.34-1.016-.02-1.865.108-2.432.481L2.598 18.39l2.108 2.645.048-.031c.417-.397.97-.62 1.547-.62h6.91c.152 0 .3-.038.432-.11l7.568-4.156.07-.043a.8.8 0 0 0 .318-.452l.016-.08a.8.8 0 0 0-.103-.536zm-11.95 2.637a.4.4 0 0 0 .046-.055zm4.553-.35-.064.047za1 1 0 0 0 .06-.053zm.998-.238a2 2 0 0 1-.064.197zm-2.713-1.294.156.015-.301-.034zm10.28-.226q-.006.092-.02.185zq.005-.093.002-.185zm-.906-.677-.007-.01zm-16.32-.077q-.049.016-.095.036a4 4 0 0 1 .262-.091zm16.07-.199-.072-.049-.012-.006a1 1 0 0 1 .085.055'
    />
  </Svg>
);
export default SvgOwned;
