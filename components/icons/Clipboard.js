import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgClipboard = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M11 11a1 1 0 0 1 1-1h4a1 1 0 1 1 0 2h-4a1 1 0 0 1-1-1M12 15a1 1 0 1 0 0 2h4a1 1 0 1 0 0-2zM7 11a1 1 0 0 1 1-1h.01a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1M8 15a1 1 0 1 0 0 2h.01a1 1 0 1 0 0-2z'
    />
  </Svg>
);
export default SvgClipboard;
