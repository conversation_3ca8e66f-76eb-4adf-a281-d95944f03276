import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgMic1 = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M12 14.727a3.636 3.636 0 0 0 3.636-3.636V5.636a3.636 3.636 0 1 0-7.272 0v5.455A3.636 3.636 0 0 0 12 14.727m-1.818-9.09a1.818 1.818 0 0 1 3.636 0v5.454a1.819 1.819 0 0 1-3.636 0zm9.09 5.454a.909.909 0 1 0-1.817 0 5.455 5.455 0 0 1-10.91 0 .91.91 0 0 0-1.818 0 7.27 7.27 0 0 0 6.364 7.209v1.882H9.273a.909.909 0 0 0 0 1.818h5.454a.909.909 0 0 0 0-1.818H12.91V18.3a7.27 7.27 0 0 0 6.364-7.21'
    />
  </Svg>
);
export default SvgMic1;
