import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgRuller = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='m9.614 22.054 12.55-12.551c.83-.83.869-2.137.09-2.917l-4.95-4.95c-.78-.78-2.089-.74-2.917.088L1.835 14.275c-.828.83-.868 2.137-.088 2.917l4.95 4.95c.78.78 2.088.74 2.917-.088M3.25 15.69 15.8 3.139c.041-.04.08-.057.092-.069q.008-.008.003-.014l4.92 4.903c.002.012-.004.07-.065.13l-1.325 1.325-2.122-2.121-1.414 1.414 2.121 2.121-1.414 1.415-2.828-2.829-1.414 1.414 2.828 2.829-1.414 1.414-2.122-2.121-1.414 1.414 2.122 2.121-1.415 1.415-2.828-2.829-1.414 1.414 2.828 2.829L8.2 20.64c-.056.055-.106.066-.094.082l-4.92-4.903c-.002-.012.004-.07.065-.13'
    />
  </Svg>
);
export default SvgRuller;
