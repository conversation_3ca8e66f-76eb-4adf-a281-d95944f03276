import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const ShowEye = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M9.778 17.07a.927.927 0 0 1-1.8-.451l.51-2.027a10 10 0 0 1-2.991-1.846l-1.293 1.381a.954.954 0 1 1-1.392-1.304l1.379-1.47a11.1 11.1 0 0 1-2.005-4.115c-.123-.487.208-.96.701-1.057.51-.1.998.244 1.137.745 1.016 3.676 4.202 6.36 7.976 6.36s6.96-2.684 7.976-6.36c.139-.502.626-.844 1.137-.745.493.095.824.569.7 1.056a11.1 11.1 0 0 1-2.003 4.115l1.378 1.47a.954.954 0 1 1-1.392 1.305l-1.293-1.38a10 10 0 0 1-2.99 1.845l.508 2.028a.927.927 0 0 1-1.799.451l-.494-1.97a9.6 9.6 0 0 1-3.456 0z'
    />
  </Svg>
);
export default ShowEye;
