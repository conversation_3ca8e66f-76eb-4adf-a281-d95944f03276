import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgInbox = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M7.24 3a3 3 0 0 0-2.685 1.664v.001l-3.44 6.87A1 1 0 0 0 1 12v6a3 3 0 0 0 3 3h16a3 3 0 0 0 3-3v-6.038a1 1 0 0 0-.115-.428l-3.44-6.869v-.001A3 3 0 0 0 16.76 3zm13.14 8-2.724-5.442-.002-.003A1 1 0 0 0 16.76 5H7.24a1 1 0 0 0-.894.555L5.45 5.11l.894.448L3.62 11H8a1 1 0 0 1 .832.445L10.535 14h2.93l1.703-2.555A1 1 0 0 1 16 11zM3 13v5a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1v-5h-4.465l-1.703 2.555A1 1 0 0 1 14 16h-4a1 1 0 0 1-.832-.445L7.465 13z'
    />
  </Svg>
);
export default SvgInbox;
