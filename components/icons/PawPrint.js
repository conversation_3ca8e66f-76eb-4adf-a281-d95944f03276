import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgPawPrint = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M9.296 9.007A6.003 6.003 0 0 1 15 15v3.5l-.005.201a4.501 4.501 0 0 1-8.79 1.143l-.001-.003c-.33-1.063-.984-1.717-2.041-2.046l-.002-.001A4.5 4.5 0 0 1 2.57 10.09 4.5 4.5 0 0 1 5.499 9H9zM5.279 11.01a2.5 2.5 0 0 0-.523 4.874c1.688.524 2.835 1.672 3.359 3.361l.076.21a2.5 2.5 0 0 0 4.206.672 2.5 2.5 0 0 0 .593-1.405L13 18.5V15l-.004-.196a4 4 0 0 0-3.8-3.8L9.002 11H5.503zM20 13a3 3 0 1 1 0 6 3 3 0 0 1 0-6m0 2a1 1 0 1 0 0 2 1 1 0 0 0 0-2M18 5a3 3 0 1 1 0 6 3 3 0 0 1 0-6m0 2a1 1 0 1 0 0 2 1 1 0 0 0 0-2M11 1a3 3 0 1 1 0 6 3 3 0 0 1 0-6m0 2a1 1 0 1 0 0 2 1 1 0 0 0 0-2'
    />
  </Svg>
);
export default SvgPawPrint;
