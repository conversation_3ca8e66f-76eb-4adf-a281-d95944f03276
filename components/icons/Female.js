import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgFemale = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M12 5.027a3.71 3.71 0 0 1 3.706 3.706A3.71 3.71 0 0 1 12 12.438a3.71 3.71 0 0 1-3.706-3.705A3.71 3.71 0 0 1 12 5.027m1.059 9.434a5.824 5.824 0 0 0 4.765-5.728A5.82 5.82 0 0 0 12 2.909a5.82 5.82 0 0 0-5.824 5.824 5.824 5.824 0 0 0 4.765 5.728v2.213H9.882c-.582 0-1.058.476-1.058 1.059 0 .582.476 1.058 1.058 1.058h1.06v1.06c0 .582.476 1.058 1.058 1.058s1.059-.476 1.059-1.059v-1.059h1.059c.582 0 1.059-.476 1.059-1.058s-.477-1.06-1.06-1.06H13.06z'
    />
  </Svg>
);
export default SvgFemale;
