import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgSettings = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='m21.786 9.428-1.985-.662.935-1.869a1.05 1.05 0 0 0-.2-1.197L18.3 3.463a1.05 1.05 0 0 0-1.207-.199l-1.87.935-.661-1.985a1.05 1.05 0 0 0-.987-.714h-3.15a1.05 1.05 0 0 0-.998.714l-.661 1.985-1.869-.935a1.05 1.05 0 0 0-1.197.2L3.463 5.7a1.05 1.05 0 0 0-.199 1.207l.935 1.87-1.985.661a1.05 1.05 0 0 0-.714.987v3.15a1.05 1.05 0 0 0 .714.998l1.985.661-.935 1.869a1.05 1.05 0 0 0 .2 1.197L5.7 20.536a1.05 1.05 0 0 0 1.207.2l1.87-.934.661 1.984a1.05 1.05 0 0 0 .997.714h3.15a1.05 1.05 0 0 0 .998-.714l.662-1.985 1.868.935a1.05 1.05 0 0 0 1.187-.2l2.236-2.236a1.05 1.05 0 0 0 .2-1.207l-.934-1.87 1.984-.661a1.05 1.05 0 0 0 .714-.987v-3.15a1.05 1.05 0 0 0-.714-.998M20.4 12.819l-1.26.42a2.1 2.1 0 0 0-1.218 2.961l.598 1.197-1.154 1.155-1.166-.63a2.098 2.098 0 0 0-2.93 1.218l-.42 1.26h-1.669l-.42-1.26A2.1 2.1 0 0 0 7.8 17.922l-1.197.598-1.155-1.154.63-1.166a2.1 2.1 0 0 0-1.218-2.961l-1.26-.42v-1.638l1.26-.42A2.1 2.1 0 0 0 6.078 7.8l-.599-1.165L6.635 5.48l1.165.598a2.1 2.1 0 0 0 2.961-1.218l.42-1.26h1.638l.42 1.26A2.1 2.1 0 0 0 16.2 6.078l1.197-.599 1.155 1.156-.63 1.165a2.098 2.098 0 0 0 1.218 2.93l1.26.42zM12 7.8a4.2 4.2 0 1 0 0 8.4 4.2 4.2 0 0 0 0-8.4m0 6.3a2.1 2.1 0 1 1 0-4.2 2.1 2.1 0 0 1 0 4.2'
    />
  </Svg>
);
export default SvgSettings;
