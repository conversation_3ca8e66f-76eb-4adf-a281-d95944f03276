import * as React from "react"
import Svg, { Path, Rect } from "react-native-svg"

function VetAssistIcon(props) {
  return (
    <Svg
      width={182}
      height={62}
      viewBox="0 0 182 62"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.432 4.958c6.83-5.326 15.712-6.908 22.366-.073 3.319 4.085 2.352 7.124 4.085 8.17 1.517.916 9.364 4.33 11.31 *************.367.38.272.63-.656 1.723-3.21 7.948-6.22 9.006-.972.342-2.15.394-3.478.258a68.2 68.2 0 011.626 3.231c1.21 2.534 2.264 4.87 3.619 6.88l.052.07c.14.182.424.484.842.91.335.34.802.804 1.13 1.2l.13.164.128.178c.267.396.472.857.53 1.34l.01.11.007.125c.02.538-.08 1.097-.209 1.587l-.06.221a17.764 17.764 0 01-.516 1.465c-.186.476-.32.798-.408 1.048-.159.45-.55 1.726-1.185 2.72l-.13.194c-.42.597-1.024 1.2-1.841 1.53l-.167.062c-.486.167-1.122.252-1.673.303-.45.042-.946.069-1.449.081l-.503.007a25.897 25.897 0 01-1.947-.06c-.472-.034-.996-.089-1.417-.188l-.174-.045a3.628 3.628 0 01-1.276-.7l-.15-.128c-.722-.644-1.502-1.157-2.388-1.798-.966 1.365-2.607 3.203-3.582 3.855-4.12 2.751-5.867-.546-6.11.898-.187 1.109.295 3.437 1.715 4.766 11.942 2.59 24.719-2.646 31.148-13.781 7.961-13.79 3.22-31.486-10.57-39.448-5.702-3.292-5.057-5.39 1.07-1.852 14.81 8.55 19.902 27.558 11.352 42.37C49.82 60.246 30.812 65.34 16 56.788 1.192 48.24-3.901 29.23 4.65 14.42c.476-.824.994-1.62 1.548-2.383 1.64-2.531 3.8-5.179 6.235-7.078zm19.624 5.639a.837.837 0 00-.8.354l-3.215 4.631A26.67 26.67 0 009.62 23.51l-.44.45C.567 32.908 2.88 38.518 9.41 48.476c.78-2.828 3.084-8.389 7.254-10.998.4-.266.824-.5 1.275-.697l.274-.112c4.368-1.709 7.592-2 9.167-2.017.233-.003.432 0 .595.005.291.01.492.274.446.562-.202 1.255-.653 4.344-.272 4.685.48.427 2.134.78 2.336 1.258.124.293.17 1.49.971 2.956.508.875 1.303 1.85 2.573 2.795.905.675 1.998 1.38 2.896 2.181.26.233.511.38.736.445.428.125 1.452.207 2.554.221l.475.002c1.18-.006 2.362-.098 2.944-.26l.108-.034c.38-.13.719-.403 1.008-.768l.12-.162c.4-.568.692-1.332.905-1.939l.188-.541c.177-.504.537-1.325.791-2.094l.101-.325c.145-.497.222-.958.197-1.304-.011-.151-.098-.399-.309-.677-.438-.576-1.601-1.614-2.118-2.293l-.094-.132c-2.807-4.16-4.39-9.608-7.534-13.714l-.31-.393c-.277-.343-1.1-1.75-1.686-3.164l-.114-.283a8.658 8.658 0 01-.4-1.268l-.033-.166c-.542-3.02-.645-5.798-1.622-8.803l-.098-.291a.837.837 0 00-.62-.543l-.058-.01z"
        fill="#256B74"
      />
      <Path
        d="M86.373 32.282c-.672 0-1.166-.346-1.481-1.04l-9.234-21.114c-.294-.715-.326-1.303-.095-1.765.231-.462.673-.693 1.324-.693.735 0 1.24.325 1.513.977l8.099 19.444h-.347L94.41 8.647c.168-.379.357-.63.567-.757.231-.147.536-.22.914-.22.63 0 1.061.23 1.292.693.252.441.252.935 0 1.481l-9.328 21.398a1.92 1.92 0 01-.599.788c-.23.169-.525.252-.882.252zm21.156 0c-.462 0-.851-.157-1.166-.472a1.585 1.585 0 01-.473-1.166V9.308c0-.483.158-.872.473-1.166a1.584 1.584 0 011.166-.472h14.244c.484 0 .872.147 1.166.44.295.274.442.642.442 1.104 0 .441-.147.798-.442 1.071-.294.273-.682.41-1.166.41h-12.606v7.658h9.045c.483 0 .872.147 1.166.441.294.273.441.63.441 1.072 0 .441-.147.809-.441 1.103-.294.273-.683.41-1.166.41h-9.045v7.878h12.606c.484 0 .872.137 1.166.41.295.273.442.63.442 1.071 0 .463-.147.84-.442 1.135-.294.273-.682.41-1.166.41h-14.244zm32.434 0c-.462 0-.85-.157-1.166-.472a1.584 1.584 0 01-.472-1.166V7.67h3.277v22.974c0 .462-.157.85-.472 1.166a1.586 1.586 0 01-1.167.473zm-8.445-21.587c-.442 0-.809-.137-1.103-.41-.274-.294-.41-.662-.41-1.103 0-.44.136-.798.41-1.071.294-.294.661-.441 1.103-.441h16.86c.441 0 .798.147 1.071.44.295.274.442.631.442 1.072 0 .441-.147.81-.442 1.103-.273.273-.63.41-1.071.41h-16.86z"
        fill="#246A73"
      />
      <Rect
        x={159.245}
        y={7.28253}
        width={21.8046}
        height={15.3196}
        rx={4.08523}
        fill="#F3DFC1"
      />
      <Path
        d="M164.751 18.442a.419.419 0 01-.376-.21.452.452 0 01-.026-.446l2.46-5.874c.087-.205.23-.307.429-.307.21 0 .353.102.429.307l2.468 5.891a.463.463 0 01-.035.447c-.081.128-.207.192-.376.192a.505.505 0 01-.254-.07.454.454 0 01-.166-.21l-2.171-5.392h.245l-2.206 5.392a.433.433 0 01-.184.21.446.446 0 01-.237.07zm.464-1.505l.351-.753h3.431l.35.753h-4.132zm8.694 1.505a.441.441 0 01-.324-.131.442.442 0 01-.131-.324v-5.926c0-.135.044-.243.131-.324a.442.442 0 01.324-.132.42.42 0 01.324.132c.088.081.132.19.132.324v5.926a.439.439 0 01-.132.324.42.42 0 01-.324.131zM79.824 53.996c-.419 0-.74-.18-.964-.538-.224-.358-.246-.74-.067-1.143l6.297-15.037c.224-.523.59-.785 1.098-.785.538 0 .904.262 1.098.785l6.32 15.082c.164.418.135.8-.09 1.143-.209.329-.53.493-.963.493-.224 0-.44-.06-.65-.18a1.165 1.165 0 01-.426-.537L85.92 39.474h.628l-5.648 13.805a1.11 1.11 0 01-.47.538c-.195.12-.396.179-.605.179zm1.187-3.855l.897-1.927h8.785l.896 1.927H81.011zm25.08 4.079a8.33 8.33 0 01-2.734-.448 7.08 7.08 0 01-2.263-1.255c-.642-.538-1.113-1.158-1.412-1.86-.15-.314-.15-.605 0-.874.165-.27.433-.441.807-.516.269-.06.53-.015.784.135.269.134.471.343.605.627.18.374.471.717.874 1.031.419.314.919.568 1.502.762a5.773 5.773 0 001.837.291c.718 0 1.375-.112 1.973-.336.612-.224 1.105-.545 1.479-.963.373-.434.56-.964.56-1.592 0-.791-.314-1.501-.941-2.129-.613-.627-1.629-1.015-3.048-1.165-1.853-.18-3.309-.724-4.37-1.636-1.061-.911-1.591-2.04-1.591-3.384 0-.986.276-1.822.829-2.51.553-.687 1.3-1.21 2.241-1.569.956-.358 2.017-.537 3.182-.537.912 0 1.703.127 2.376.38a5.63 5.63 0 011.793 1.054c.508.448.948.979 1.322 1.591.209.329.291.642.246.941a.883.883 0 01-.425.673c-.284.179-.591.216-.919.112a1.262 1.262 0 01-.74-.583c-.239-.433-.53-.8-.874-1.098a3.71 3.71 0 00-1.21-.695c-.463-.18-1.009-.269-1.636-.269-1.106-.015-2.032.194-2.779.628-.747.418-1.12 1.068-1.12 1.95 0 .448.112.88.336 1.3.239.403.665.754 1.277 1.052.628.3 1.524.508 2.689.628 1.778.18 3.16.725 4.146 1.636 1.001.896 1.502 2.084 1.502 3.563 0 .852-.179 1.591-.538 2.219a4.789 4.789 0 01-1.412 1.591 6.271 6.271 0 01-2.017.941 8.607 8.607 0 01-2.331.314zm19.688 0c-.957 0-1.868-.15-2.735-.448a7.08 7.08 0 01-2.263-1.255c-.642-.538-1.113-1.158-1.412-1.86-.149-.314-.149-.605 0-.874.164-.27.433-.441.807-.516.269-.06.53-.015.784.135.269.134.471.343.605.627.18.374.471.717.874 1.031.419.314.919.568 1.502.762a5.774 5.774 0 001.838.291c.717 0 1.374-.112 1.972-.336.612-.224 1.105-.545 1.479-.963.373-.434.56-.964.56-1.592 0-.791-.314-1.501-.941-2.129-.613-.627-1.629-1.015-3.048-1.165-1.853-.18-3.309-.724-4.37-1.636-1.061-.911-1.591-2.04-1.591-3.384 0-.986.276-1.822.829-2.51.553-.687 1.3-1.21 2.241-1.569.956-.358 2.017-.537 3.182-.537.912 0 1.703.127 2.376.38a5.63 5.63 0 011.793 1.054c.508.448.948.979 1.322 1.591.209.329.291.642.246.941a.88.88 0 01-.425.673c-.284.179-.591.216-.919.112a1.266 1.266 0 01-.74-.583c-.239-.433-.53-.8-.874-1.098a3.71 3.71 0 00-1.21-.695c-.463-.18-1.008-.269-1.636-.269-1.106-.015-2.032.194-2.779.628-.747.418-1.12 1.068-1.12 1.95 0 .448.112.88.336 1.3.239.403.665.754 1.277 1.052.628.3 1.524.508 2.689.628 1.778.18 3.16.725 4.146 1.636 1.001.896 1.502 2.084 1.502 3.563 0 .852-.179 1.591-.538 2.219a4.789 4.789 0 01-1.412 1.591 6.271 6.271 0 01-2.017.941 8.605 8.605 0 01-2.33.314zm14.398-.224c-.329 0-.605-.112-.829-.336a1.124 1.124 0 01-.337-.83V37.66c0-.344.112-.62.337-.83.224-.224.5-.336.829-.336.343 0 .62.112.829.337.224.209.336.485.336.829V52.83c0 .328-.112.605-.336.829-.209.224-.486.336-.829.336zm14.56.224c-.956 0-1.868-.15-2.734-.448a7.086 7.086 0 01-2.264-1.255c-.642-.538-1.113-1.158-1.411-1.86-.15-.314-.15-.605 0-.874.164-.27.433-.441.806-.516.269-.06.531-.015.785.135.269.134.47.343.605.627.179.374.47.717.874 1.031.418.314.919.568 1.501.762a5.779 5.779 0 001.838.291c.717 0 1.375-.112 1.972-.336.613-.224 1.106-.545 1.479-.963.374-.434.56-.964.56-1.592 0-.791-.313-1.501-.941-2.129-.612-.627-1.628-1.015-3.048-1.165-1.852-.18-3.309-.724-4.37-1.636-1.06-.911-1.591-2.04-1.591-3.384 0-.986.277-1.822.829-2.51.553-.687 1.3-1.21 2.241-1.569.957-.358 2.017-.537 3.183-.537.911 0 1.703.127 2.375.38a5.62 5.62 0 011.793 1.054c.508.448.949.979 1.322 1.591.209.329.292.642.247.941a.881.881 0 01-.426.673c-.284.179-.59.216-.919.112a1.263 1.263 0 01-.739-.583 4.005 4.005 0 00-.874-1.098 3.715 3.715 0 00-1.211-.695c-.463-.18-1.008-.269-1.635-.269-1.106-.015-2.032.194-2.779.628-.747.418-1.121 1.068-1.121 1.95 0 .448.112.88.336 1.3.239.403.665.754 1.278 1.052.627.3 1.524.508 2.689.628 1.778.18 3.16.725 4.146 1.636 1.001.896 1.501 2.084 1.501 3.563 0 .852-.179 1.591-.538 2.219a4.786 4.786 0 01-1.411 1.591 6.271 6.271 0 01-2.017.941 8.607 8.607 0 01-2.331.314zm17.512-.224c-.328 0-.605-.112-.829-.336a1.127 1.127 0 01-.336-.83V36.494h2.331v16.338c0 .328-.112.605-.336.829a1.13 1.13 0 01-.83.336zm-6.006-15.351c-.313 0-.575-.097-.784-.292a1.109 1.109 0 01-.291-.784c0-.314.097-.568.291-.762.209-.21.471-.314.784-.314h11.99c.314 0 .568.105.762.314.209.194.314.448.314.762s-.105.575-.314.784c-.194.195-.448.292-.762.292h-11.99z"
        fill="#246A73"
      />
    </Svg>
  )
}

export default VetAssistIcon
