import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgDoor = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M6.864 5.064A.9.9 0 0 1 7.5 4.8h6.75a.9.9 0 1 0 0-1.8H7.5a2.7 2.7 0 0 0-2.7 2.7v13.5h-.9a.9.9 0 1 0 0 1.8h16.2a.9.9 0 1 0 0-1.8h-2.7v-5.85a.9.9 0 1 0-1.8 0v5.85h-9V5.7a.9.9 0 0 1 .264-.636'
    />
  </Svg>
);
export default SvgDoor;
