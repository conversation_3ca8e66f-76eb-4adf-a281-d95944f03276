import * as React from "react"
import Svg, { Path } from "react-native-svg"

function TethoScope(props) {
  return (
    <Svg
      width={27}
      height={26}
      viewBox="0 0 27 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.916 2.167c.65 0 1.083.433 1.083 1.083 0 .65-.433 1.083-1.083 1.083H4.833V9.75a4.346 4.346 0 004.333 4.333 4.346 4.346 0 004.333-4.333V4.333h-1.083c-.65 0-1.083-.433-1.083-1.083 0-.65.433-1.083 1.083-1.083h2.167c.65 0 1.083.433 1.083 1.083v6.5c0 3.142-2.275 5.85-5.417 6.392v.65a4.855 4.855 0 004.875 4.875A4.855 4.855 0 0020 16.792V14.95c-1.3-.434-2.166-1.625-2.166-3.033 0-1.842 1.408-3.25 3.25-3.25 1.3 0 2.6.866 3.033 2.166.65 1.734-.217 3.575-1.95 4.117v1.842c0 3.9-3.142 7.041-7.042 7.041a7.028 7.028 0 01-7.041-7.041v-.65C4.94 15.6 2.666 12.892 2.666 9.75v-6.5c0-.65.433-1.083 1.083-1.083h2.167zm15.167 8.666a1.083 1.083 0 100 2.167 1.083 1.083 0 000-2.167z"
        fill="#B3B3B3"
      />
    </Svg>
  )
}

export default TethoScope
