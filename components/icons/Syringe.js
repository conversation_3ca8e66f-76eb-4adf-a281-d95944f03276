import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgSyringe = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M21.714 2.296a1 1 0 0 0-1.42 0l-2.81 2.78-.71-.7a1 1 0 0 0-1.41 0l-.72.75-.71-.71a1 1 0 0 0-1.42 0l-7.77 7.78-.74-.7a1 1 0 0 0-1.38 1.41l3.53 3.54-1.73 1.74-.71-.72a1.004 1.004 0 1 0-1.42 1.42l2.83 2.83a1 1 0 0 0 .71.29 1 1 0 0 0 .71-1.71l-.71-.7 1.74-1.74 3.53 3.53a1 1 0 0 0 .71.3 1 1 0 0 0 .7-1.71l-.7-.71 7.78-7.77a1 1 0 0 0 0-1.42l-.71-.71.72-.7a1 1 0 0 0 0-1.41l-.7-.71 2.81-2.78a1 1 0 0 0 0-1.47m-11.32 15.54L6.11 13.44 7.5 12l4.314 4.426zm2.83-2.83-4.22-4.22 4.24-4.24 4.24 4.24zm2.84-5.64-1.442-1.442 1.452-1.428.7.7.7.7z'
    />
  </Svg>
);
export default SvgSyringe;
