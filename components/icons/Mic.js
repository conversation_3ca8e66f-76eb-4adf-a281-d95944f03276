import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgMic = ({ width = 40, height = 40, color = '#256B74', ...props }) => (
  <Svg
    width={width}
    height={height}
    viewBox="0 0 40 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <Path
      d="M20 22.7273C20.9644 22.7273 21.8894 22.3442 22.5713 21.6622C23.2533 20.9803 23.6364 20.0553 23.6364 19.0909V13.6364C23.6364 12.6719 23.2533 11.747 22.5713 11.0651C21.8894 10.3831 20.9644 10 20 10C19.0356 10 18.1107 10.3831 17.4287 11.0651C16.7468 11.747 16.3637 12.6719 16.3637 13.6364V19.0909C16.3637 20.0553 16.7468 20.9803 17.4287 21.6622C18.1107 22.3442 19.0356 22.7273 20 22.7273ZM18.1818 13.6364C18.1818 13.1542 18.3734 12.6917 18.7144 12.3507C19.0553 12.0097 19.5178 11.8182 20 11.8182C20.4822 11.8182 20.9447 12.0097 21.2857 12.3507C21.6266 12.6917 21.8182 13.1542 21.8182 13.6364V19.0909C21.8182 19.5731 21.6266 20.0356 21.2857 20.3766C20.9447 20.7175 20.4822 20.9091 20 20.9091C19.5178 20.9091 19.0553 20.7175 18.7144 20.3766C18.3734 20.0356 18.1818 19.5731 18.1818 19.0909V13.6364ZM27.2727 19.0909C27.2727 18.8498 27.177 18.6186 27.0065 18.4481C26.836 18.2776 26.6048 18.1818 26.3637 18.1818C26.1226 18.1818 25.8913 18.2776 25.7208 18.4481C25.5503 18.6186 25.4546 18.8498 25.4546 19.0909C25.4546 20.5375 24.8799 21.9249 23.857 22.9479C22.834 23.9708 21.4467 24.5455 20 24.5455C18.5534 24.5455 17.166 23.9708 16.1431 22.9479C15.1202 21.9249 14.5455 20.5375 14.5455 19.0909C14.5455 18.8498 14.4497 18.6186 14.2792 18.4481C14.1087 18.2776 13.8775 18.1818 13.6364 18.1818C13.3953 18.1818 13.164 18.2776 12.9936 18.4481C12.8231 18.6186 12.7273 18.8498 12.7273 19.0909C12.7289 20.8612 13.3761 22.5701 14.5477 23.8972C15.7192 25.2244 17.3345 26.0787 19.0909 26.3V28.1818H17.2727C17.0316 28.1818 16.8004 28.2776 16.6299 28.4481C16.4594 28.6186 16.3637 28.8498 16.3637 29.0909C16.3637 29.332 16.4594 29.5632 16.6299 29.7337C16.8004 29.9042 17.0316 30 17.2727 30H22.7273C22.9684 30 23.1996 29.9042 23.3701 29.7337C23.5406 29.5632 23.6364 29.332 23.6364 29.0909C23.6364 28.8498 23.5406 28.6186 23.3701 28.4481C23.1996 28.2776 22.9684 28.1818 22.7273 28.1818H20.9091V26.3C22.6655 26.0787 24.2809 25.2244 25.4524 23.8972C26.6239 22.5701 27.2711 20.8612 27.2727 19.0909Z"
      fill={color}
    />
  </Svg>
);

export default SvgMic;
