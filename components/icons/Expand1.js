import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgExpand1 = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M20.615 3C21.38 3 22 3.62 22 4.385V8a1 1 0 1 1-2 0V5.364c0-.201-.163-.364-.364-.364H17a1 1 0 1 1 0-2zM7 3a1 1 0 0 1 0 2H4.364C4.163 5 4 5.163 4 5.364V8a1 1 0 0 1-2 0V4.385C2 3.62 2.62 3 3.385 3zm12.636 16c.201 0 .364-.163.364-.364V16a1 1 0 1 1 2 0v3.615C22 20.38 21.38 21 20.615 21H17a1 1 0 1 1 0-2zM4 18.636c0 .201.163.364.364.364H7a1 1 0 1 1 0 2H3.385C2.62 21 2 20.38 2 19.615V16a1 1 0 1 1 2 0z'
    />
  </Svg>
);
export default SvgExpand1;
