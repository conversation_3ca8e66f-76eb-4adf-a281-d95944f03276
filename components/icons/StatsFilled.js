import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgStatsFilled = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M5 2h14a3 3 0 0 1 3 3v14a3 3 0 0 1-3 3H5a3 3 0 0 1-3-3V5a3 3 0 0 1 3-3m6.293 4.293A1 1 0 0 1 13 7v10a1 1 0 0 1-2 0V7a1 1 0 0 1 .293-.707m-5 6A1 1 0 0 1 8 13v4a1 1 0 1 1-2 0v-4a1 1 0 0 1 .293-.707M17 10a1 1 0 0 0-1 1v6a1 1 0 0 0 2 0v-6a1 1 0 0 0-1-1'
    />
  </Svg>
);
export default SvgStatsFilled;
