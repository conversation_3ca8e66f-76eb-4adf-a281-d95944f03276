import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgQrCode1 = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M21 20a1 1 0 0 1 1 1v.01a1 1 0 0 1-2 0V21a1 1 0 0 1 1-1M7 15a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2zm5 4a1 1 0 0 1 1 1v1a1 1 0 1 1-2 0v-1a1 1 0 0 1 1-1m9-4a1 1 0 1 1 0 2h-3a1 1 0 0 0-1 1v3a1 1 0 1 1-2 0v-3a3 3 0 0 1 3-3zM4 20h3v-3H4zm8-5a1 1 0 0 1 1 1v.01a1 1 0 1 1-2 0V16a1 1 0 0 1 1-1m9-4a1 1 0 0 1 1 1v.01a1 1 0 1 1-2 0V12a1 1 0 0 1 1-1m-17.888.005a1 1 0 0 1 0 1.99L3.01 13H3a1 1 0 1 1 0-2h.01zM12 6a1 1 0 0 1 1 1v3a3 3 0 0 1-3 3H7a1 1 0 1 1 0-2h3a1 1 0 0 0 1-1V7a1 1 0 0 1 1-1m5 5a1 1 0 1 1 0 2h-1a1 1 0 1 1 0-2zM7 2a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2zm13 0a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-3a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2zM4 7h3V4H4zm13 0h3V4h-3zm-4.888-4.995a1 1 0 0 1 0 1.99L12.01 4H12a1 1 0 1 1 0-2h.01z'
    />
  </Svg>
);
export default SvgQrCode1;
