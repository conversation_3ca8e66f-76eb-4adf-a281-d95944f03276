import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

const SvgCotton = ({ width = 24, height = 24, color = '#160F29', ...props }) => (
  <Svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    {...props}
  >
    <Path
      fill={color}
      d='M11 15.25v2.537a.5.5 0 0 0 .076.267.7.7 0 0 0 .109.138.9.9 0 0 0 .228.16 1.2 1.2 0 0 0 .346.113 1.4 1.4 0 0 0 .624-.033q.183-.053.324-.151a.74.74 0 0 0 .217-.227.5.5 0 0 0 .076-.267V15.25h1.25a3.75 3.75 0 0 0 1.276-7.277A3.75 3.75 0 0 0 12 5.5a3.75 3.75 0 0 0-3.527 2.474 3.749 3.749 0 0 0 1.277 7.277zM15 10a.75.75 0 0 1-.75-.75 2.25 2.25 0 0 0-4.5 0 .752.752 0 0 1-1.024.698A.75.75 0 0 1 8.4 9.7a2.25 2.25 0 0 0 .986 4.021A3 3 0 0 1 9 12.251a.75.75 0 0 1 1.5 0 1.5 1.5 0 0 0 .5 1.117v-1.184c0-.185.105-.363.293-.494.187-.131.442-.205.707-.205s.52.074.707.205c.188.13.293.309.293.494v1.168l.06-.056c.281-.282.44-.663.44-1.06a.75.75 0 0 1 1.5 0c0 .526-.14 1.038-.396 1.487A2.25 2.25 0 0 0 15.6 9.7a.76.76 0 0 1-.313.243A.8.8 0 0 1 15 10'
    />
  </Svg>
);
export default SvgCotton;
