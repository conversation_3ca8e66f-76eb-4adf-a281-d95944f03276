import { Text, TextProps, TextStyle, StyleProp } from 'react-native';

import { textStyles } from './service';

interface TextTypesProps extends TextProps {
  type: string;
  color?: string;
  customStyle?: StyleProp<TextStyle>;
}

const TextTypes = ({
  type,
  children,
  color,
  customStyle,
  ...props
}: TextTypesProps) => {
  const styles = textStyles(type);
  // Only override color if color prop is provided
  const styleArray = color ? [styles, { color }, customStyle] : [styles, customStyle];
  return (
    <Text {...props} style={styleArray}>
      {children}
    </Text>
  );
};

export default TextTypes;
