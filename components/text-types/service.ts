import styles from './styles';

export const textStyles = (type: string) => {
  switch (type) {
    case 'h1':
      return styles().h1;
    case 'h2':
      return styles().h2;
    case 'h3':
      return styles().h3;
    case 'h4':
      return styles().h4;
    case 'h5':
      return styles().h5;
    case 'body1':
      return styles().body1;
    case 'body2':
      return styles().body2;
    case 'small':
      return styles().small;
    case 'label':
      return styles().label;
    case 'label2':
      return styles().label2;
    case 'buttonText':
      return styles().buttonText;
    case 'errorText':
      return styles().errorText;
    // Registration flow types:
    case 'body3':
      return styles().body3;
    default:
      return styles().body1;
  }
};
