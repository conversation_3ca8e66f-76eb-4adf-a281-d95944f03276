import TextTypes from '../text-types';
import React from 'react';

import { COLOURS } from '@/constants/colours';

import { SectionTitleProps } from './services';
import { styles } from './styles';

const SectionTitle = ({ title, subtitle }: SectionTitleProps) => {
  const { title: titleStyle, subtitle: subtitleStyle } = styles;
  return (
    <>
      <TextTypes type='h2' color={COLOURS.primary} customStyle={titleStyle}>
        {title}
      </TextTypes>
      <TextTypes
        type='body1'
        color={COLOURS.secondaryTint}
        customStyle={subtitleStyle}
      >
        {subtitle}
      </TextTypes>
    </>
  );
};

export default SectionTitle;
