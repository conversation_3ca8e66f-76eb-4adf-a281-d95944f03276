import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

export const styles = StyleSheet.create({
  inputView: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginVertical: 10,
    justifyContent: 'space-between',
  },
  inputViewDisabled: {
    backgroundColor: COLOURS.greyLight,
    borderColor: COLOURS.greyLight,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: COLOURS.backgroundModal,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: COLOURS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
    paddingTop: 24,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    marginBottom: 24,
  },
  closeButton: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitle: {
    textAlign: 'center',
  },
  optionsList: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLOURS.borderColor,
  },
  selectedOptionItem: {
    backgroundColor: COLOURS.grey,
  },
  optionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  marginBottom12: {
    marginBottom: 12,
  },
});
