import React, { useState } from 'react';
import { Modal, TouchableOpacity, View, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import type { ModalDropdownProps, DropdownOption } from './services';
import { getDisplayText, getTextColor, getChevronColor, getModalTitle } from './services';
import { styles } from './styles';

const ModalDropdown: React.FC<ModalDropdownProps> = ({
  value,
  onChange,
  label,
  placeholder = 'Select option',
  options,
  disabled = false,
  customStyle,
  inline = false,
}) => {
  const [show, setShow] = useState(false);

  const {
    inputView,
    inputViewDisabled,
    modalOverlay,
    modalContent,
    modalHeader,
    closeButton,
    modalTitle,
    optionsList,
    optionItem,
    selectedOptionItem,
    optionText,
    marginBottom12,
  } = styles;

  const handleOpen = () => {
    if (!disabled) {
      setShow(true);
    }
  };

  const handleOptionSelect = (option: DropdownOption) => {
    onChange(option);
    setShow(false);
  };

  const containerStyle = inline ? {} : marginBottom12;

  return (
    <View style={containerStyle}>
      {label && !inline && (
        <TextTypes type='h5' color={COLOURS.textBlack}>
          {label}
        </TextTypes>
      )}
      <TouchableOpacity
        style={[inputView, disabled && inputViewDisabled, customStyle]}
        onPress={handleOpen}
        activeOpacity={0.8}
        disabled={disabled}
      >
        <TextTypes
          type={'buttonText'}
          color={getTextColor(disabled, value)}
        >
          {getDisplayText(value, placeholder)}
        </TextTypes>
        <Ionicons
          name='chevron-down'
          size={18}
          color={getChevronColor(disabled)}
        />
      </TouchableOpacity>

      <Modal
        visible={show}
        animationType='slide'
        transparent
        onRequestClose={() => setShow(false)}
      >
        <View style={modalOverlay}>
          <View style={modalContent}>
            <View style={modalHeader}>
              <TouchableOpacity
                style={closeButton}
                onPress={() => setShow(false)}
              >
                <Ionicons name="close" size={24} color={COLOURS.textBlack} />
              </TouchableOpacity>
              <TextTypes type='h4' color={COLOURS.primary} customStyle={modalTitle}>
                {getModalTitle(label)}
              </TextTypes>
              <View style={{ width: 24 }} />
            </View>

            <ScrollView style={optionsList} showsVerticalScrollIndicator={false}>
              {options.map((option, index) => {
                const isSelected = value?.value === option.value;
                return (
                  <TouchableOpacity
                    key={option.value || index}
                    style={[optionItem, isSelected && selectedOptionItem]}
                    onPress={() => handleOptionSelect(option)}
                    activeOpacity={0.7}
                  >
                    <TextTypes
                      type='buttonText'
                      color={COLOURS.textBlack}
                      customStyle={optionText}
                    >
                      {option.label}
                    </TextTypes>
                    {isSelected && (
                      <Ionicons
                        name="checkmark"
                        size={20}
                        color={COLOURS.primary}
                      />
                    )}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ModalDropdown;
