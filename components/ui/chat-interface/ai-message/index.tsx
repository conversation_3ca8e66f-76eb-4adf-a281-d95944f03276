import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import Markdown from 'react-native-markdown-display';
import SvgCopy from '@/components/icons/MessageCopy';
import SvgLike from '@/components/icons/MessageLike';
import SvgUnlike from '@/components/icons/Unlike';
import TextTypes from '@/components/text-types';
import type { ChatItem } from '@/types/chat';
import { COLOURS } from '@/constants/colours';
import styles from './styles';

interface AiMessageProps {
  item: ChatItem;
  COLOURS: typeof COLOURS;
  copyFeedbackId: string | null;
  handleCopy: (text: string, id: string) => void;
}

export default function AiMessage({ item, COLOURS, copyFeedbackId, handleCopy }: AiMessageProps) {
  return (
    <View
      style={[
        {
          alignSelf: 'flex-start',
          backgroundColor: 'transparent',
          width: '100%',
        },
        styles.messageContainer,
        { borderBottomLeftRadius: 0, paddingHorizontal: 4 },
      ]}
    >
      <Markdown style={{ body: styles.markDownTextStyle }}>
        {item.text}
      </Markdown>
      <View style={styles.iconContainer}>
        <TouchableOpacity onPress={() => handleCopy(item.text, item.id)}>
          <SvgCopy width={24} height={24} style={{ marginLeft: 8 }} />
        </TouchableOpacity>
        {copyFeedbackId === item.id && (
          <TextTypes customStyle={styles.copyText} color={COLOURS.primary} type={'body2'}>Copied!</TextTypes>
        )}
        <SvgLike width={24} height={24} style={{ marginLeft: 8 }} />
        <SvgUnlike width={24} height={24} style={{ marginLeft: 8 }} />
      </View>
    </View>
  );
}
