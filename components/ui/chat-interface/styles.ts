import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  chatView: {
    flex: 1,
  },
  footerContainer: {
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
    borderRadius: 16,
    marginVertical: 4,
    padding: 12,
  },
  markDownTextStyle: { color: COLOURS.textBlack, fontSize: 16 },
  loaderContainer: { alignSelf: 'center', marginTop: 8 },
  footerLoaderContainer: {
    alignSelf: 'center',
    marginVertical: 4,
    marginLeft: 0,
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  listStyle: { padding: 16, paddingBottom: 6 },
  bottomView: {
    backgroundColor: COLOURS.white,
    paddingHorizontal: 20,
    paddingTop: 15,
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
    borderTopEndRadius: 25,
    borderTopStartRadius: 25,
  },
  inputBarWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 8,
  },
  inputBar: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: COLOURS.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLOURS.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    minHeight: 56,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    maxHeight: 100,
    paddingHorizontal: 8,
    paddingVertical: 12,
    color: COLOURS.textBlack,
  },
  actionButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rowButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sendIcon: {
    marginLeft: 8,
  },
});

export default styles; 