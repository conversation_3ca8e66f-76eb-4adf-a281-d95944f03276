import { useState } from 'react';
import { FlatList, View, TextInput, TouchableOpacity } from 'react-native';
import Markdown from 'react-native-markdown-display';
import Loader from '@/components/ui/Loader';
import UserMessage from './user-message';
import AiMessage from './ai-message';
import SvgAdd from '@/components/icons/Add';
import SvgMic from '@/components/icons/Mic';
import SvgSend from '@/components/icons/Send';
import type { ChatItem } from '@/types/chat';
import type { EdgeInsets } from 'react-native-safe-area-context';
import type { RefObject } from 'react';
import { COLOURS } from '@/constants/colours';
import styles from './styles';

interface ChatInterfaceProps {
  messages: ChatItem[];
  onSend: (input: string) => void;
  loading: boolean;
  animatingMessage: string | null;
  isTyping: boolean;
  copyFeedbackId: string | null;
  handleCopy: (text: string, id: string) => void;
  insects: EdgeInsets;
  COLOURS: typeof COLOURS;
  flatListRef: RefObject<FlatList<any>>;
  scrollToBottom: () => void;
}

export default function ChatInterface({
  messages,
  onSend,
  loading,
  animatingMessage,
  isTyping,
  copyFeedbackId,
  handleCopy,
  insects,
  COLOURS,
  flatListRef,
  scrollToBottom
}: ChatInterfaceProps) {
  const [input, setInput] = useState('');
  const [isFocused, setIsFocused] = useState(false);

  const handleSendInternal = () => {
    if (!input.trim() || loading) return;
    onSend(input);
    setInput('');
  };

  return (
    <>
      <View style={styles.chatView}>
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={({ item }: any) =>
            item.sender === 'user' ? (
              <UserMessage item={item} COLOURS={COLOURS} />
            ) : (
              <AiMessage
                item={item}
                COLOURS={COLOURS}
                copyFeedbackId={copyFeedbackId}
                handleCopy={handleCopy}
              />
            )
          }
          keyExtractor={(item) => item.id}
          onContentSizeChange={scrollToBottom}
          onLayout={scrollToBottom}
          ListFooterComponent={() => (
            <>
              {animatingMessage && (
                <View style={styles.footerContainer}>
                  <Markdown style={{ body: styles.markDownTextStyle }}>
                    {animatingMessage}
                  </Markdown>
                  {isTyping && (
                    <View style={styles.loaderContainer}>
                      <Loader />
                    </View>
                  )}
                </View>
              )}
              {loading && !animatingMessage && (
                <View style={styles.footerLoaderContainer}>
                  <Loader />
                </View>
              )}
            </>
          )}
          contentContainerStyle={styles.listStyle}
        />
      </View>
      <View style={[styles.bottomView, { paddingBottom: insects.bottom + 10 }]}>
        <View style={styles.inputBarWrapper}>
          <View
            style={[
              styles.inputBar,
              {
                borderColor: isFocused
                  ? COLOURS.primary
                  : COLOURS.borderColor,
              },
            ]}
          >
            <TextInput
              style={styles.textInput}
              placeholder='Ask VetAssist'
              value={input}
              onChangeText={setInput}
              multiline
              selectionColor={COLOURS.primary}
              placeholderTextColor={COLOURS.placeholderText}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
            />
          </View>
        </View>
        <View style={styles.actionButtonContainer}>
          <SvgAdd color={COLOURS.primary} />
          <View style={styles.rowButton}>
            <SvgMic color={COLOURS.primary} />
            <TouchableOpacity
              onPress={handleSendInternal}
              style={styles.sendIcon}
              activeOpacity={input.trim() ? 0.7 : 1}
              disabled={!input.trim() || loading}
            >
              <SvgSend
                color={input ? COLOURS.whiteColor : COLOURS.borderColor}
                bgColor={input ? COLOURS.primary : COLOURS.grayIcon}
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </>
  );
}
