import React from 'react';
import { View } from 'react-native';
import TextTypes from '@/components/text-types';
import type { ChatItem } from '@/types/chat';
import { COLOURS } from '@/constants/colours';
import styles from './styles';

interface UserMessageProps {
  item: ChatItem;
  COLOURS: typeof COLOURS;
}

export default function UserMessage({ item, COLOURS }: UserMessageProps) {
  return (
    <View
      style={[
        {
          alignSelf: 'flex-end',
          backgroundColor: COLOURS.orange,
        },
        styles.messageContainer,
        { borderTopRightRadius: 0 },
      ]}
    >
      <TextTypes customStyle={styles.youText} type='errorText' color={COLOURS.youText}>
        You
      </TextTypes>
      <TextTypes
        style={styles.messageText}
        type={'body2'}
        color={COLOURS.textBlack}
      >
        {item.text}
      </TextTypes>
    </View>
  );
}
