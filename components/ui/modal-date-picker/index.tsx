import React, { useState } from 'react';
import { Modal, TouchableOpacity, View } from 'react-native';
import { Calendar } from 'react-native-calendars';

import { CalendarIcon, Cancel } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import type { ModalDatePickerProps } from './services';
import {
  formatDateForCalendar,
  getCalendarTheme,
  getDisplayText,
  getMarkedDates,
  getTextColor,
} from './services';
import { styles } from './styles';

const ModalDatePicker: React.FC<ModalDatePickerProps> = ({
  value,
  onChange,
  label,
  placeholder = 'DD/MM/YYYY',
  maximumDate,
  minimumDate,
}) => {
  const [show, setShow] = useState(false);

  const {
    inputView,
    modalOverlay,
    modalContent,
    modalHeader,
    closeButton,
    modalTitle,
    calendarContainer,
    marginBottom12,
  } = styles;

  const handleOpen = () => {
    setShow(true);
  };

  const handleDateSelect = (day: any) => {
    const selectedDate = new Date(day.dateString);
    onChange(selectedDate);
    setShow(false);
  };

  return (
    <View style={marginBottom12}>
      <TextTypes type='h5' color={COLOURS.textBlack}>
        {label}
      </TextTypes>
      <TouchableOpacity
        style={inputView}
        onPress={handleOpen}
        activeOpacity={0.8}
      >
        <TextTypes type={'buttonText'} color={getTextColor(value)}>
          {getDisplayText(value, placeholder)}
        </TextTypes>
        <CalendarIcon />
      </TouchableOpacity>

      <Modal
        visible={show}
        animationType='slide'
        transparent
        onRequestClose={() => setShow(false)}
      >
        <View style={modalOverlay}>
          <View style={modalContent}>
            <View style={modalHeader}>
              <TouchableOpacity
                style={closeButton}
                onPress={() => setShow(false)}
              >
                <Cancel color={COLOURS.primary} />
              </TouchableOpacity>
              <TextTypes
                type='h4'
                color={COLOURS.primary}
                customStyle={modalTitle}
              >
                {label}
              </TextTypes>
              <View style={{ width: 24 }} />
            </View>

            <View style={calendarContainer}>
              <Calendar
                current={value ? formatDateForCalendar(value) : undefined}
                markedDates={getMarkedDates(value)}
                onDayPress={handleDateSelect}
                theme={getCalendarTheme()}
                hideExtraDays={true}
                firstDay={0}
                enableSwipeMonths={true}
                maxDate={
                  maximumDate ? formatDateForCalendar(maximumDate) : undefined
                }
                minDate={
                  minimumDate ? formatDateForCalendar(minimumDate) : undefined
                }
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ModalDatePicker;
