import { COLOURS } from '@/constants/colours';
import { formatDateDDMMYYYY } from '@/utils/date';
import { FONTS } from '@/constants/fonts';

export interface ModalDatePickerProps {
  value?: Date;
  onChange: (date: Date) => void;
  label: string;
  placeholder?: string;
  maximumDate?: Date;
  minimumDate?: Date;
}

// Helper functions that can be moved to services
export const formatDateForCalendar = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

export const getMarkedDates = (value?: Date) => {
  if (!value) return {};

  const dateString = formatDateForCalendar(value);
  return {
    [dateString]: {
      selected: true,
      selectedColor: '#D4A574',
      selectedTextColor: '#FFFFFF',
    },
  };
};

export const getCalendarTheme = () => ({
  backgroundColor: COLOURS.white,
  calendarBackground: COLOURS.white,
  textSectionTitleColor: COLOURS.primary,
  selectedDayBackgroundColor: COLOURS.tertiaryShade,
  selectedDayTextColor: COLOURS.white,
  todayTextColor: COLOURS.tertiaryShade,
  dayTextColor: COLOURS.dayText,
  textDisabledColor: COLOURS.diableText,
  dotColor: COLOURS.tertiaryShade,
  selectedDotColor: COLOURS.white,
  arrowColor: COLOURS.primary,
  disabledArrowColor: COLOURS.diableText,
  monthTextColor: COLOURS.primary,
  indicatorColor: COLOURS.tertiaryShade,
  textDayFontFamily: FONTS.semiBold,
  textMonthFontFamily: FONTS.bold,
  textDayHeaderFontFamily: FONTS.semiBold,
  textDayFontSize: 16.74,
  textMonthFontSize: 18,
  textDayHeaderFontSize: 14,
});

export const getDisplayText = (value?: Date, placeholder: string = 'DD/MM/YYYY'): string => {
  return value ? formatDateDDMMYYYY(value) : placeholder;
};

export const getTextColor = (value?: Date): string => {
  return value ? COLOURS.textBlack : COLOURS.youText;
};

export const handleDateSelection = (day: any, onChange: (date: Date) => void): Date => {
  return new Date(day.dateString);
};
