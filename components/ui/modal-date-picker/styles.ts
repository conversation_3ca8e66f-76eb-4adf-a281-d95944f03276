import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

export const styles = StyleSheet.create({
  inputView: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 16,
    padding: 16,
    marginVertical: 10,
    justifyContent: 'space-between',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: COLOURS.backgroundModal,
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: COLOURS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingVertical: 24,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    marginBottom: 10,
  },
  closeButton: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitle: {
    textAlign: 'center',
  },
  calendarContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  marginBottom12: {
    marginBottom: 12,
  },
});
