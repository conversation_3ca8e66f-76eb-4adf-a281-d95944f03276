import React from 'react';
import { View } from 'react-native';

import { TethoScope } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import { PetDetailsComponentProps } from './services';
import styles from './styles';

const PetDetailsComponent: React.FC<PetDetailsComponentProps> = ({ pet }) => {
  const {
    section,
    detailRow,
    vetEmptyContainer,
    vetEmptySubText,
    vetEmptyText,
    sectionTitle,
    genderText,
  } = styles;

  return (
    <>
      <View style={section}>
        <TextTypes
          type='h4'
          color={COLOURS.primary}
          customStyle={sectionTitle}
        >
          About
        </TextTypes>
        <View style={detailRow}>
          <TextTypes type='errorText' color={COLOURS.secondaryTint}>
            Breed
          </TextTypes>
          <TextTypes type='h5' color={COLOURS.textBlack}>
            {pet.breed}
          </TextTypes>
        </View>
        <View style={detailRow}>
          <TextTypes type='errorText' color={COLOURS.secondaryTint}>
            Date of birth
          </TextTypes>
          <TextTypes type='h5' color={COLOURS.textBlack}>
            {pet.dateOfBirth}
          </TextTypes>
        </View>
        <View style={detailRow}>
          <TextTypes type='errorText' color={COLOURS.secondaryTint}>
            Microchip number
          </TextTypes>
          <TextTypes type='h5' color={COLOURS.textBlack}>
            {pet.microchipNumber}
          </TextTypes>
        </View>
        <View style={detailRow}>
          <TextTypes type='errorText' color={COLOURS.secondaryTint}>
            Species
          </TextTypes>
          <TextTypes type='h5' color={COLOURS.textBlack}>
            {pet.species}
          </TextTypes>
        </View>
        <View style={detailRow}>
          <TextTypes type='errorText' color={COLOURS.secondaryTint}>
            Gender
          </TextTypes>
          <TextTypes
            type='h5'
            color={COLOURS.textBlack}
            customStyle={genderText}
          >
            {pet.gender}
          </TextTypes>
        </View>
        <View style={detailRow}>
          <TextTypes type='errorText' color={COLOURS.secondaryTint}>
            Weight
          </TextTypes>
          <TextTypes type='h5' color={COLOURS.textBlack}>
            {pet.weight}
          </TextTypes>
        </View>
        <View style={detailRow}>
          <TextTypes type='errorText' color={COLOURS.secondaryTint}>
            Insurance
          </TextTypes>
          <TextTypes type='h5' color={COLOURS.textBlack}>
            {pet.insuranceProvider}
          </TextTypes>
        </View>
      </View>
      {/* Registered Vets Section */}
      <View style={section}>
        <TextTypes type='h4' color={COLOURS.primary}>
          Registered vets
        </TextTypes>
        <View style={vetEmptyContainer}>
          <TethoScope />
          <TextTypes
            type='h5'
            color={COLOURS.grayIcon}
            customStyle={vetEmptyText}
          >
            No vet added yet
          </TextTypes>
          <TextTypes
            type='errorText'
            color={COLOURS.grayIcon}
            customStyle={vetEmptySubText}
          >
            Tap Edit to add a vet for {pet.name}.
          </TextTypes>
        </View>
      </View>
    </>
  );
};

export default PetDetailsComponent;
