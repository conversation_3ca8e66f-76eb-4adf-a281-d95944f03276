import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  section: {
    backgroundColor: COLOURS.white,
    borderRadius: 20,
    paddingHorizontal: 14,
    paddingTop: 20,
    paddingBottom: 4,
    marginVertical: 8,
    shadowColor: COLOURS.black,
    shadowOpacity: 0.04,
    shadowRadius: 12,
    elevation: 2,
    width: '90%',
    shadowOffset: { width: 0, height: 4 },
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
  },
  sectionTitle: {
    marginBottom: 14,
  },
  genderText: {
    textTransform: 'capitalize'
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  vetEmptyContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  vetEmptyText: {
    marginTop: 6,
    marginBottom: 5
  },
  vetEmptySubText: {
    textAlign: 'center',
  },
});

export default styles;
