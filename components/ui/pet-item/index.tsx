import React from 'react';
import { View, Image, TouchableOpacity } from 'react-native';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';
import styles from './styles';
import { PetItemProps } from './services';

interface PetItemPropsWithPress extends PetItemProps {
  onPress?: (id: number) => void;
}

const PetItem: React.FC<PetItemPropsWithPress> = ({ image, name, breed, sex, age, weight, id, onPress }) => {
  const {
    card,
    petImage,
    petInfoContainer,
    petNameBreedContainer,
    petNameContainer,
    petName,
    petBreed,
    petDetailsRow,
    petDetailBox,
    petDetailValue,
    deviderLine
  } = styles;

  return (
    <TouchableOpacity onPress={onPress ? () => onPress(id) : undefined} activeOpacity={0.85}>
      <View style={card}>
        <Image source={image} style={petImage} />
        <View style={petInfoContainer}>
          <View style={petNameBreedContainer}>
            <View style={petNameContainer}>
              <TextTypes type='h2' customStyle={petName} color={COLOURS.primary}>
                {name}
              </TextTypes>
              <TextTypes type='h5' customStyle={petBreed} color={COLOURS.secondaryTint}>
                {breed}
              </TextTypes>
            </View>
          </View>
          <View style={petDetailsRow}>
            <View style={petDetailBox}>
              <TextTypes type='h5' color={COLOURS.primary} customStyle={petDetailValue}>
                {sex}
              </TextTypes>
              <TextTypes type='errorText' color={COLOURS.darkGrayText}>
                Sex
              </TextTypes>
            </View>
            <View style={deviderLine} />
            <View style={petDetailBox}>
              <TextTypes type='h5' color={COLOURS.primary} customStyle={petDetailValue}>
                {age}
              </TextTypes>
              <TextTypes type='errorText' color={COLOURS.darkGrayText}>
                Age
              </TextTypes>
            </View>
            <View style={deviderLine} />
            <View style={petDetailBox}>
              <TextTypes type='h5' color={COLOURS.primary} customStyle={petDetailValue}>
                {weight}
              </TextTypes>
              <TextTypes type='errorText' color={COLOURS.darkGrayText}>
                Weight
              </TextTypes>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default PetItem; 