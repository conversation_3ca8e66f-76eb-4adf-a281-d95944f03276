import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  card: {
    backgroundColor: COLOURS.white,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
    paddingVertical: 16,
    paddingHorizontal: 20,
    alignItems: 'center',
    shadowColor: COLOURS.black,
    shadowOpacity: 0.03,
    shadowRadius: 8,
    marginTop: 16,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2
  },
  petImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 12,
    resizeMode: 'cover',
    backgroundColor: COLOURS.lightGreen,
  },
  petInfoContainer: {
    alignItems: 'center',
    width: '100%',
  },
  petNameBreedContainer: {
    alignItems: 'center',
    marginBottom: 10,
  },
  petNameContainer: {
    alignItems: 'center',
    marginTop: 4
  },
  petName: {
    marginBottom: 2,
  },
  petBreed: {
    marginTop: 2,
    color: COLOURS.secondaryTint,
  },
  petDetailsRow: {
    flexDirection: 'row',
    backgroundColor: COLOURS.lightPrimary,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: COLOURS.mediumPrimary,
    width: '100%',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 8,
    marginTop: 8,
  },
  petDetailBox: {
    flex: 1,
    paddingVertical: 2,
    alignItems: 'center',
  },
  petDetailValue: {
    marginBottom: 6,
    textTransform: 'capitalize'
  },
  deviderLine: {
    width: 1,
    backgroundColor: COLOURS.mediumPrimary,
    height: '100%'
  }
});

export default styles; 