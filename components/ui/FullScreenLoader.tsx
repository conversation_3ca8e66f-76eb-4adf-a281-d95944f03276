import React from 'react';
import { Modal, View, StyleSheet } from 'react-native';
import Loader from './Loader';
import { COLOURS } from '@/constants/colours';

const FullScreenLoader = ({ visible = false }: { visible: boolean }) => (
  <Modal
    visible={visible}
    transparent
    animationType="fade"
    statusBarTranslucent
  >
    <View style={styles.overlay}>
      <Loader />
    </View>
  </Modal>
);

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: COLOURS.loaderBackground,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default FullScreenLoader;
