export interface Language {
  label: string;
  value: string;
}

export const LANGUAGES: Language[] = [
  { label: 'English (UK)', value: 'en-GB' },
  { label: '(Arabic) عربي', value: 'ar' },
  { label: '<PERSON><PERSON><PERSON> (German)', value: 'de' },
  { label: 'English (US)', value: 'en-US' },
  { label: '<PERSON><PERSON><PERSON> (Spanish)', value: 'es' },
  { label: 'Italiano (Italian)', value: 'it' },
  { label: 'Português (Portuguese)', value: 'pt' },
  { label: 'แบบไทย (Thai)', value: 'th' },
];

export interface LanguageSelectorProps {
  visible: boolean;
  onClose: () => void;
  languages: Language[];
  selectedLanguage: string;
  onSelect: (value: string) => void;
} 