import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: COLOURS.modalBackground,
    justifyContent: 'flex-end',
  },
  sheet: {
    backgroundColor: COLOURS.white,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 16,
    paddingBottom: 32,
    minHeight: '60%',
    shadowColor: COLOURS.textBlack,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 10,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 24,
  },
  closeButton: {
    padding: 8,
    marginRight: 8,
  },
  closeText: {
    fontSize: 28,
    color: COLOURS.primary,
    fontWeight: '300',
  },
  title: {
    flex: 1,
    textAlign: 'center',
  },
  searchContainer: {
    marginBottom: 16,
    paddingHorizontal: 24,
  },
  searchInputWrapper: {
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    marginVertical: 10,
  },
  searchInputField: {
    flex: 1,
    marginLeft: 8,
    paddingTop: 0,
    paddingBottom: 0
  },
  languageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLOURS.greyMedium,
    paddingHorizontal: 24,
  },
  radioOuter: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLOURS.grayIcon,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedOuter: {
    borderWidth: 2,
    borderColor: COLOURS.primary,
  },
  radioInner: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: COLOURS.primary,
  },
  closeIcon: {
    width: 36,
    height: 36,
  },
});

export default styles; 