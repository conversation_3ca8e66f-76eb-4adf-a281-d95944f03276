import React, { useState, useMemo } from 'react';
import {
  FlatList,
  Modal,
  TextInput,
  TouchableOpacity,
  View,
  ListRenderItem,
} from 'react-native';

import { CloseIcon, SearchIcon } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import { LanguageSelectorProps, Language } from './services';
import styles from './styles';

const {
  overlay,
  sheet,
  header,
  title,
  searchContainer,
  searchInputWrapper,
  searchInputField,
  languageRow,
  radioOuter,
  selectedOuter,
  closeIcon,
  radioInner,
} = styles;

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  visible,
  onClose,
  languages,
  selectedLanguage,
  onSelect,
}) => {
  const [search, setSearch] = useState('');

  const filteredLanguages = useMemo(
    () =>
      languages.filter((lang) =>
        lang.label.toLowerCase().includes(search.toLowerCase())
      ),
    [languages, search]
  );

  const renderItem: ListRenderItem<Language> = ({ item }) => {
    const isSelected = selectedLanguage === item.value;
    return (
      <TouchableOpacity
        style={languageRow}
        onPress={() => onSelect(item.value)}
      >
        <TextTypes type='body3' color={COLOURS.textBlack}>
          {item.label}
        </TextTypes>
        <View style={[radioOuter, isSelected && selectedOuter]}>
          {isSelected && <View style={radioInner} />}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType='slide'
      transparent
      onRequestClose={onClose}
    >
      <View style={overlay}>
        <View style={sheet}>
          <View style={header}>
            <TouchableOpacity onPress={onClose}>
              <CloseIcon />
            </TouchableOpacity>
            <TextTypes type='h3' color={COLOURS.primary} customStyle={title}>
              Language
            </TextTypes>
            <View style={closeIcon} />
          </View>
          <View style={searchContainer}>
            <View style={searchInputWrapper}>
              <SearchIcon />
              <TextInput
                style={searchInputField}
                placeholder='Search'
                value={search}
                onChangeText={setSearch}
                placeholderTextColor={COLOURS.secondaryTint}
              />
            </View>
          </View>
          <FlatList
            data={filteredLanguages}
            keyExtractor={(item) => item.value}
            renderItem={renderItem}
            keyboardShouldPersistTaps='handled'
          />
        </View>
      </View>
    </Modal>
  );
};

export default LanguageSelector;
