import LottieView from 'lottie-react-native';
import React from 'react';
import { StyleSheet, View } from 'react-native';

const Loader = () => (
  <View style={styles.container}>
    <LottieView
      source={require('@/assets/animation/three-dot-loader.json')}
      autoPlay
      loop
      style={styles.loader}
    />
  </View>
);

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loader: { width: 80, height: 80 },
});

export default Loader;
