import { StyleSheet } from 'react-native';

import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  formGroup: {
    marginBottom: 20,
  },
  formGroupDropDown: {
    marginBottom: 8,
  },
  weightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 16,
    marginVertical: 10,
  },
  weightInput: {
    flex: 1,
    padding: 16,
    fontSize: 16,
    fontFamily: 'Comfortaa-Regular',
    color: COLOURS.textBlack,
  },
  verticalDivider: {
    width: 1,
    height: 40,
    backgroundColor: COLOURS.greyMedium,
  },
  weightUnitDropdown: {
    borderWidth: 0,
    paddingVertical: 5
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 16,
    padding: 16,
    marginVertical: 10,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    fontFamily: 'Comfortaa-Regular',
    color: COLOURS.textBlack,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginBottom: 4,
    marginTop: 14,
  },
  qrCode: { left: -1.5 },
  codeInput: {
    flex: 1,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    height: 50,
    marginVertical: 0,
  },
  referralContainer: {
    alignItems: 'center',
    marginTop: 3,
  },
});

export default styles;
