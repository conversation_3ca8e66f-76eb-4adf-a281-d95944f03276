import { StyleSheet, Dimensions } from 'react-native';
import { COLOURS } from '@/constants/colours';

const { width } = Dimensions.get('window');
const SCAN_SIZE = width * 0.8;

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLOURS.qrBackground,
  },
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLOURS.qrBackground,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 10,
    backgroundColor: COLOURS.qrBackground,
  },
  closeBtn: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  time: {
    color: COLOURS.white,
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: 2,
  },
  cameraWrapper: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLOURS.textBlack,
  },
  camera: {
    ...StyleSheet.absoluteFillObject,
  },
  scanFrameWrapper: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -SCAN_SIZE / 2,
    marginTop: -SCAN_SIZE / 2,
    width: SCAN_SIZE,
    height: SCAN_SIZE,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scanFrame: {
    width: SCAN_SIZE,
    height: SCAN_SIZE,
    borderRadius: 16,
    borderWidth: 0,
    position: 'relative',
    overflow: 'visible',
  },
  corner: {
    position: 'absolute',
    width: 32,
    height: 32,
    borderColor: COLOURS.yellowFrame,
    borderWidth: 4,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    borderTopLeftRadius: 12,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
    borderTopRightRadius: 12,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
    borderBottomLeftRadius: 12,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderBottomRightRadius: 12,
  },
  bottomSection: {
    backgroundColor: COLOURS.white,
    alignItems: 'center',
    height: '25%',
  },
  flashBtn: {
    backgroundColor: COLOURS.qrBackground,
    width: '100%',
    height: '34%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  instruction: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  torchIcon: {
    position: 'absolute',
    bottom: -30,
  },
  textAlign: {
    textAlign: 'center',
  },
});
