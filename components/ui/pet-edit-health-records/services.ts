import { EditPetFormData } from '@/app/(tabs)/my-pets/edit-pet/services';
import { DueSoon, Overdue, UpToDate } from '@/components/icons';
import { COLOURS } from '@/constants/colours';

import styles from './styles';

export interface PetEditHealthRecordsProps {
  values: EditPetFormData;
  errors: any;
  touched: any;
  handleChange: any;
  handleBlur: any;
  setFieldValue: (field: string, value: any) => void;
  submitCount: number;
  vaccinations: VaccinationRecord[];
  setVaccinations: (vaccinations: VaccinationRecord[]) => void;
  treatments: TreatmentRecord[];
  setTreatments: (treatments: TreatmentRecord[]) => void;
}

export interface VaccinationRecord {
  id: number;
  name: string;
  date: string;
  status: 'up-to-date' | 'overdue' | 'due-soon';
}

export interface TreatmentRecord {
  id: number;
  name: string;
  date: string | null;
  status: 'up-to-date' | 'overdue' | 'due-soon';
}

// Use the same functions as pet-health-records component
export const getColor = (status: string) => {
  switch (status) {
    case 'up-to-date':
      return COLOURS.primary;
    case 'overdue':
      return COLOURS.redText;
    case 'due-soon':
      return COLOURS.goldText;
    default:
      return COLOURS.primary;
  }
};

export const StatusIcon = (status: string) => {
  switch (status) {
    case 'up-to-date':
      return UpToDate;
    case 'overdue':
      return Overdue;
    case 'due-soon':
      return DueSoon;
    default:
      return UpToDate;
  }
};

export const getStatusText = (status: string) => {
  switch (status) {
    case 'up-to-date':
      return 'Up to date';
    case 'overdue':
      return 'Overdue';
    case 'due-soon':
      return 'Due soon';
    default:
      return 'Up to date';
  }
};

export const getStatusStyle = (status: string) => {
  const { statusUpToDate, statusOverdue, statusDueSoon } = styles;
  switch (status) {
    case 'up-to-date':
      return statusUpToDate;
    case 'overdue':
      return statusOverdue;
    case 'due-soon':
      return statusDueSoon;
    default:
      return statusUpToDate;
  }
};

// Mock vaccination types for dropdown
export const vaccinationTypes = [
  { label: 'Rabies', value: 'Rabies' },
  { label: 'Parvovirus', value: 'Parvovirus' },
  { label: 'Kennel Cough', value: 'Kennel Cough' },
  { label: 'Distemper', value: 'Distemper' },
  { label: 'Hepatitis', value: 'Hepatitis' },
];

// Mock treatment types for dropdown
export const treatmentTypes = [
  { label: 'Select Treatment', value: '' },
  { label: 'Worming', value: 'Worming' },
  { label: 'Flea Treatment', value: 'Flea Treatment' },
  { label: 'Tick Prevention', value: 'Tick Prevention' },
  { label: 'Heartworm Prevention', value: 'Heartworm Prevention' },
];
