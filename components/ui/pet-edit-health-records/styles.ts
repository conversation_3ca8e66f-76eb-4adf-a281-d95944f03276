import { StyleSheet } from 'react-native';

import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  section: {
    backgroundColor: COLOURS.white,
    borderRadius: 20,
    paddingTop: 20,
    marginBottom: 20,
    shadowColor: COLOURS.black,
    shadowOpacity: 0.04,
    shadowRadius: 12,
    elevation: 2,
    shadowOffset: { width: 0, height: 4 },
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
  },
  marginH20: { marginHorizontal: 20 },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 14,
    marginTop: 10,
  },
  sectionTitle: {
    textTransform: 'capitalize',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLOURS.primary,
    paddingLeft: 5,
    paddingRight: 10,
    paddingVertical: 4,
    borderRadius: 16,
  },
  addButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  vaccinationItem: {
    marginBottom: 24,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: COLOURS.borderColor,
  },
  vaccinationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  deleteButton: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: COLOURS.primary,
  },
  vaccinationInput: {
    marginBottom: 16,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  statusBadge: {
    paddingHorizontal: 7,
    paddingVertical: 3,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 5,
  },
  statusUpToDate: {
    backgroundColor: COLOURS.mediumPrimary,
  },
  statusOverdue: {
    backgroundColor: COLOURS.lightRed,
  },
  statusDueSoon: {
    backgroundColor: COLOURS.lightYellow,
  },
  noMargin: {
    borderBottomWidth: 0,
    marginBottom: 0,
  },
  treatmentItem: {
    marginBottom: 24,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: COLOURS.borderColor,
    position: 'relative',
  },
  treatmentDropdown: {
    marginBottom: 16,
  },
  disableTrash: {
    backgroundColor: COLOURS.borderColor,
    borderColor: COLOURS.borderColor,
  }
});

export default styles;
