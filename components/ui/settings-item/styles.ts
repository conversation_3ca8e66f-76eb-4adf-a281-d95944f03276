import { I18nManager, StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 6,
    borderBottomWidth: 1,
    borderBottomColor: COLOURS.borderColor,
    backgroundColor: 'transparent',
    position: 'relative',
  },
  listLabel: {
    flex: 1,
    marginHorizontal: 8,
    textAlign: 'left'
  },
  badge: {
    backgroundColor: COLOURS.upgardeBg,
    borderRadius: 14,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginLeft: 8,
    marginRight: 14,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  listRight: {
    marginHorizontal: 12,
  },
  ultimateText: {
    marginHorizontal: 4,
  },
});

export default styles; 