import React from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';

import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import styles from './styles';

import type { SettingsItemProps } from '@/types/settings';

const { listItem, listLabel, badge, listRight, ultimateText } = styles;

const SettingsItem: React.FC<SettingsItemProps> = ({
  item,
  style,
  onPress,
}) => {
  const { t } = useTranslation();
  return (
    <TouchableOpacity
      style={[listItem, style]}
      onPress={onPress}
    >
      <item.icon />
      <TextTypes
        color={COLOURS.textBlack}
        type='h5'
        customStyle={listLabel}
      >
        {t(item.label)}
      </TextTypes>
      {item.badge && (
        <View style={badge}>
          <item.badge.icon />
          <TextTypes
            color={COLOURS.textBlack}
            type='h5'
            customStyle={ultimateText}
          >
            {t(item.badge.label)}
          </TextTypes>
        </View>
      )}
      {item.rightText && (
        <TextTypes
          type='body3'
          customStyle={listRight}
          color={COLOURS.secondaryTint}
        >
          {t(item.rightText)}
        </TextTypes>
      )}
      {item.rightIcon && <item.rightIcon />}
    </TouchableOpacity>
  );
};

export default SettingsItem;
