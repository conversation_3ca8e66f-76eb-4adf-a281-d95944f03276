import { ComponentType } from 'react';
import { AddChatIcon, PetMenuIcon, HistoryMenuIcon, SettingMenuIcon } from '@/components/icons';

export type DrawerMenuItem = {
  key: string;
  icon: ComponentType<any>;
  label: string;
  badgeCount?: number;
};

export const menuItems: DrawerMenuItem[] = [
  {
    key: 'new-chat',
    icon: AddChatIcon,
    label: 'drawer_new_chat',
  },
  {
    key: 'my-pets',
    icon: PetMenuIcon,
    label: 'drawer_my_pets',
    badgeCount: 1,
  },
  {
    key: 'chat-history',
    icon: HistoryMenuIcon,
    label: 'drawer_chat_history',
  },
  {
    key: 'settings',
    icon: SettingMenuIcon,
    label: 'settings',
  },
]; 