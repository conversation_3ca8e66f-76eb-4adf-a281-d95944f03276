import React from 'react';
import { StyleProp, TextStyle, TouchableOpacity } from 'react-native';

import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import styles from './styles';

interface ButtonProps {
  title: string;
  onPress: () => void;
  disabled?: boolean;
  style?: any;
  textStyle?: StyleProp<TextStyle>;
}

export default function Button({
  title,
  onPress,
  disabled,
  style,
  textStyle,
}: ButtonProps) {
  const { button, buttonDisabled, btnText } = styles;
  return (
    <TouchableOpacity
      style={[button, disabled && buttonDisabled, style]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <TextTypes
        type='buttonText'
        customStyle={[textStyle, btnText]}
        color={disabled ? COLOURS.grayIcon : COLOURS.white}
      >
        {title}
      </TextTypes>
    </TouchableOpacity>
  );
}
