import React from 'react';
import { TouchableOpacity, View } from 'react-native';

import { Date, OralPill, Video } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import {
  PetHealthRecordsComponentProps,
  StatusIcon,
  chatHistory,
  getColor,
  getStatusStyle,
  getStatusText,
  vaccinations,
  vetVisits,
} from './services';
import styles from './styles';

const PetHealthRecordsComponent: React.FC<PetHealthRecordsComponentProps> = ({
  pet,
}) => {
  const {
    section,
    sectionTitle,
    vaccinationItem,
    vaccinationName,
    vaccinationStatus,
    emptySection,
    emptyText,
    emptySubText,
    vetVisitItem,
    vetVisitHeader,
    vetVisitDate,
    vetVisitDuration,
    vetVisitTitle,
    vetVisitReason,
    transcriptButton,
    transcriptButtonText,
    chatHistoryItem,
    chatHistoryDate,
    chatHistoryTitle,
    chatHistoryTime,
    smallIcon,
    chatChildRow,
    mb10,
    borderB0,
  } = styles;

  return (
    <>
      {/* Vaccinations Section */}
      <View style={section}>
        <TextTypes type='h4' color={COLOURS.primary} customStyle={sectionTitle}>
          Vaccinations
        </TextTypes>
        {vaccinations.map((vaccination, index) => (
          <View key={index} style={vaccinationItem}>
            <TextTypes
              type='h5'
              color={COLOURS.textBlack}
              customStyle={vaccinationName}
            >
              {vaccination.name}
            </TextTypes>
            <View
              style={[vaccinationStatus, getStatusStyle(vaccination.status)]}
            >
              {React.createElement(StatusIcon(vaccination.status))}
              <TextTypes type='errorText' color={getColor(vaccination.status)}>
                {getStatusText(vaccination.status)}
              </TextTypes>
            </View>
          </View>
        ))}
      </View>

      {/* Treatments & Preventatives Section */}
      <View style={section}>
        <TextTypes type='h4' color={COLOURS.primary} customStyle={sectionTitle}>
          treatments & preventatives
        </TextTypes>
        <View style={emptySection}>
          <OralPill color={COLOURS.grayIcon} />
          <TextTypes type='h5' color={COLOURS.grayIcon} customStyle={emptyText}>
            No treatments & preventatives logged
          </TextTypes>
          <TextTypes
            type='label'
            color={COLOURS.grayIcon}
            customStyle={emptySubText}
          >
            Tap Edit to record dates, current medication or worming dates
          </TextTypes>
        </View>
      </View>

      {/* Veterinary Visits & Consultations Section */}
      <View style={section}>
        <TextTypes type='h4' color={COLOURS.primary} customStyle={sectionTitle}>
          Veterinary visits & consultations
        </TextTypes>
        {vetVisits.map((visit, index) => (
          <View key={index} style={vetVisitItem}>
            <View style={vetVisitHeader}>
              <View style={smallIcon}>
                <Date width={18} height={18} color={COLOURS.secondaryTint} />
                <TextTypes
                  type='errorText'
                  color={COLOURS.secondaryTint}
                  customStyle={vetVisitDate}
                >
                  {visit.date}
                </TextTypes>
              </View>
              <View style={smallIcon}>
                <Video width={18} height={18} color={COLOURS.secondaryTint} />
                <TextTypes
                  type='errorText'
                  color={COLOURS.secondaryTint}
                  customStyle={vetVisitDuration}
                >
                  {visit.duration}
                </TextTypes>
              </View>
            </View>
            <TextTypes
              type='h5'
              color={COLOURS.textBlack}
              customStyle={vetVisitTitle}
            >
              {visit.title}
            </TextTypes>
            <TextTypes
              type='body3'
              color={COLOURS.textBlack}
              customStyle={vetVisitReason}
            >
              Reason: {visit.reason}
            </TextTypes>
            <TouchableOpacity style={transcriptButton}>
              <TextTypes
                type='h5'
                color={COLOURS.primary}
                customStyle={transcriptButtonText}
              >
                View Transcript
              </TextTypes>
            </TouchableOpacity>
          </View>
        ))}
      </View>

      {/* Chat History Section */}
      <View style={section}>
        <TextTypes
          type='h4'
          color={COLOURS.primary}
          customStyle={[sectionTitle, mb10]}
        >
          {pet.name}'s latest chat history
        </TextTypes>
        <TextTypes
          type='errorText'
          color={COLOURS.primary}
          customStyle={chatHistoryDate}
        >
          June 2025
        </TextTypes>
        {chatHistory.map((chat, index) => (
          <View
            key={index}
            style={[
              chatHistoryItem,
              index === chatHistory.length - 1 && borderB0,
            ]}
          >
            <TextTypes
              type='h5'
              color={COLOURS.textBlack}
              customStyle={chatHistoryTitle}
            >
              {chat.title}
            </TextTypes>
            <View style={chatChildRow}>
              <TextTypes
                type='errorText'
                color={COLOURS.secondaryTint}
                customStyle={chatHistoryTime}
              >
                {chat.time}
              </TextTypes>
              {chat.duration && (
                <View style={smallIcon}>
                  <Video width={18} height={18} color={COLOURS.secondaryTint} />
                  <TextTypes
                    type='errorText'
                    color={COLOURS.secondaryTint}
                    customStyle={vetVisitDuration}
                  >
                    {chat.duration}
                  </TextTypes>
                </View>
              )}
            </View>
          </View>
        ))}
      </View>
    </>
  );
};

export default PetHealthRecordsComponent;
