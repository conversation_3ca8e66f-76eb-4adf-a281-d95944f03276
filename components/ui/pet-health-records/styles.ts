import { StyleSheet } from 'react-native';

import { COLOURS } from '@/constants/colours';

const styles = StyleSheet.create({
  section: {
    backgroundColor: COLOURS.white,
    borderRadius: 20,
    paddingHorizontal: 14,
    paddingTop: 20,
    paddingBottom: 4,
    marginVertical: 8,
    shadowColor: COLOURS.black,
    shadowOpacity: 0.04,
    shadowRadius: 12,
    elevation: 2,
    width: '90%',
    shadowOffset: { width: 0, height: 4 },
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
  },
  mb10: {
    marginBottom: 12,
  },
  sectionTitle: {
    marginBottom: 18,
    textTransform: 'capitalize',
  },
  vaccinationItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  vaccinationName: {
    flex: 1,
  },
  vaccinationStatus: {
    paddingHorizontal: 7,
    paddingVertical: 3,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 5,
  },
  statusUpToDate: {
    backgroundColor: COLOURS.mediumPrimary,
  },
  statusOverdue: {
    backgroundColor: COLOURS.lightRed,
  },
  statusDueSoon: {
    backgroundColor: COLOURS.lightYellow,
  },
  emptySection: {
    alignItems: 'center',
    marginBottom: 15,
  },
  emptyText: {
    marginTop: 8,
    marginBottom: 6,
    textAlign: 'center',
  },
  emptySubText: {
    textAlign: 'center',
    lineHeight: 18,
  },
  vetVisitItem: {
    marginBottom: 12,
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  vetVisitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  smallIcon: { flexDirection: 'row', gap: 5, alignItems: 'center' },
  chatChildRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  vetVisitDate: {
    lineHeight: 18,
    marginTop: 1,
  },
  vetVisitDuration: {
    lineHeight: 18,
    marginTop: 1,
  },
  vetVisitTitle: {
    marginVertical: 4,
    lineHeight: 21,
  },
  vetVisitReason: {
    marginBottom: 12,
    lineHeight: 18,
  },
  transcriptButton: {
    paddingVertical: 7,
    paddingHorizontal: 16,
    width: '100%',
  },
  transcriptButtonText: {
    fontSize: 12,
    letterSpacing: 0.5,
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  borderB0: { borderBottomWidth: 0, paddingBottom: 0 },
  chatHistoryItem: {
    marginBottom: 14,
    paddingBottom: 6,
    borderBottomWidth: 1,
    borderBottomColor: COLOURS.borderColor,
  },
  chatHistoryDate: {
    marginBottom: 16,
    backgroundColor: COLOURS.lightPrimary,
    paddingVertical: 4,
    paddingHorizontal: 16,
    alignSelf: 'flex-start',
    borderRadius: 12,
    lineHeight: 18,
  },
  chatHistoryTitle: {
    flex: 1,
    marginRight: 12,
    lineHeight: 21,
  },
  chatHistoryTime: {
    fontSize: 12,
    lineHeight: 15,
    marginTop: 3,
  },
});

export default styles;
