import DateTimePicker from '@react-native-community/datetimepicker';
import React, { useState } from 'react';
import { Modal, Platform, Text, TouchableOpacity, View } from 'react-native';

import { CalendarIcon } from '@/components/icons';
import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';
import { formatDateDDMMYYYY } from '@/utils/date';

import type { DatePickerFieldProps } from './services';
import { styles } from './styles';

const DatePickerField: React.FC<DatePickerFieldProps> = ({
  value,
  onChange,
  label,
  placeholder = 'DD/MM/YYYY',
  maximumDate = new Date(),
}) => {
  const [show, setShow] = useState(false);
  const [tempDate, setTempDate] = useState<Date>(value || new Date());
  const {
    inputView,
    modalOverlay,
    bottomSheet,
    doneButton,
    doneButtonText,
    marginBottom12,
  } = styles;

  const handleOpen = () => {
    setTempDate(value || new Date());
    setShow(true);
  };

  return (
    <View style={marginBottom12}>
      <TextTypes type='h5' color={COLOURS.textBlack}>
        {label}
      </TextTypes>
      <TouchableOpacity
        style={inputView}
        onPress={handleOpen}
        activeOpacity={0.8}
      >
        <TextTypes
          type={'buttonText'}
          color={value ? COLOURS.textBlack : COLOURS.youText}
        >
          {value ? formatDateDDMMYYYY(value) : placeholder}
        </TextTypes>
        <CalendarIcon />
      </TouchableOpacity>
      {Platform.OS === 'ios' ? (
        <Modal
          visible={show}
          animationType='slide'
          transparent
          onRequestClose={() => setShow(false)}
        >
          <View style={modalOverlay}>
            <View style={bottomSheet}>
              <DateTimePicker
                value={tempDate}
                mode='date'
                display='spinner'
                onChange={(_, date) => {
                  if (date) setTempDate(date);
                }}
                maximumDate={maximumDate}
              />
              <TouchableOpacity
                style={doneButton}
                onPress={() => {
                  onChange(tempDate);
                  setShow(false);
                }}
              >
                <Text style={doneButtonText}>Done</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      ) : (
        show && (
          <DateTimePicker
            value={tempDate}
            mode='date'
            display='default'
            onChange={(_, date) => {
              setShow(false);
              if (date) onChange(date);
            }}
            maximumDate={maximumDate}
          />
        )
      )}
    </View>
  );
};

export default DatePickerField;
