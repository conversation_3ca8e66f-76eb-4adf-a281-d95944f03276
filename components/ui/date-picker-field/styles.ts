import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

export const styles = StyleSheet.create({
  inputView: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLOURS.greyMedium,
    borderRadius: 16,
    padding: 16,
    marginVertical: 10,
    justifyContent: 'space-between',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: COLOURS.backgroundModal,
    justifyContent: 'flex-end',
  },
  bottomSheet: {
    backgroundColor: COLOURS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 24,
    paddingBottom: 32,
    alignItems: 'center',
  },
  doneButton: {
    marginTop: 16,
    backgroundColor: COLOURS.primary,
    borderRadius: 24,
    paddingVertical: 10,
    paddingHorizontal: 32,
  },
  doneButtonText: {
    color: COLOURS.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  marginBottom12: {
    marginBottom: 12,
  },
}); 