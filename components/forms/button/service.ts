import { COLOURS } from '@/constants/colours';

import styles from './styles';

export const buttonStyles = (variant: string) => {
  const styleSheet = styles();

  switch (variant) {
    case 'primary':
      return [styleSheet.base, styleSheet.primary];
    case 'secondary':
      return [styleSheet.base, styleSheet.secondary];
    case 'tertiary':
      return [styleSheet.base, styleSheet.tertiary];
    case 'outline':
      return [styleSheet.base, styleSheet.outline];
    case 'disabled':
      return [styleSheet.base, styleSheet.disabled];
    case 'small':
      return [styleSheet.smallBase, styleSheet.small];
    case 'smallSecondary':
      return [styleSheet.smallBase, styleSheet.smallSecondary];
    case 'smallTertiary':
      return [styleSheet.smallBase, styleSheet.smallTertiary];
    case 'smallOutline':
      return [styleSheet.smallBase, styleSheet.smallOutline];
    default:
      return [styleSheet.base, styleSheet.primary];
  }
};

export const getTextColor = (variant: string, customTextColor?: string) => {
  if (customTextColor) return customTextColor;

  switch (variant) {
    case 'primary':
      return COLOURS.white;
    case 'secondary':
      return COLOURS.white;
    case 'tertiary':
      return COLOURS.text;
    case 'outline':
      return COLOURS.primary;
    case 'small':
      return COLOURS.white;
    case 'smallSecondary':
      return COLOURS.white;
    case 'smallTertiary':
      return COLOURS.text;
    case 'smallOutline':
      return COLOURS.greyDark;
    case 'disabled':
      return COLOURS.greyDark;
    default:
      return COLOURS.white;
  }
};
