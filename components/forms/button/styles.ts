import { StyleSheet } from 'react-native';

import { COLOURS } from '@/constants/colours';

const borderWidth = 1;

const styles = () =>
  StyleSheet.create({
    base: {
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 32,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 48,
    },

    smallBase: {
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 40,
    },

    primary: {
      backgroundColor: COLOURS.primary,
    },
    secondary: {
      backgroundColor: COLOURS.secondary,
      borderWidth: borderWidth,
    },
    tertiary: {
      backgroundColor: COLOURS.tertiary,
    },
    outline: {
      backgroundColor: 'transparent',
      borderWidth: borderWidth,
      borderColor: COLOURS.primary,
    },
    disabled: {
      backgroundColor: COLOURS.greyMedium,
      opacity: 0.6,
    },
    small: {
      backgroundColor: COLOURS.primary,
    },
    smallSecondary: {
      backgroundColor: COLOURS.secondary,
      borderWidth: borderWidth,
    },
    smallTertiary: {
      backgroundColor: COLOURS.tertiary,
    },
    smallOutline: {
      backgroundColor: 'transparent',
      borderWidth: borderWidth,
      borderColor: COLOURS.greyDark,
    },
  });

export default styles;
