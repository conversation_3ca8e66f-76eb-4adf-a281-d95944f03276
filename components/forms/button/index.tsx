import {
  TouchableOpacity,
  TouchableOpacityProps,
  ViewStyle,
} from 'react-native';

import TextTypes from '@/components/text-types';

import { buttonStyles, getTextColor } from './service';

interface ButtonProps extends TouchableOpacityProps {
  variant?:
    | 'primary'
    | 'secondary'
    | 'outline'
    | 'tertiary'
    | 'disabled'
    | 'small'
    | 'smallSecondary'
    | 'smallOutline'
    | 'smallTertiary';
  children: string;
  customStyle?: ViewStyle;
  textColor?: string;
}

const Button = ({
  variant = 'primary',
  children,
  customStyle,
  textColor,
  disabled,
  ...props
}: ButtonProps) => {
  const buttonVariant = disabled ? 'disabled' : variant;
  const styles = buttonStyles(buttonVariant);
  const textColorValue = getTextColor(buttonVariant, textColor);

  return (
    <TouchableOpacity
      {...props}
      disabled={disabled}
      style={[...styles, customStyle]}
      activeOpacity={0.8}
    >
      <TextTypes type='buttonText' color={textColorValue}>
        {children}
      </TextTypes>
    </TouchableOpacity>
  );
};

export default Button;
