import { Checkbox as ExpoCheckbox } from 'expo-checkbox';
import { TouchableOpacity, View } from 'react-native';

import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';
import { INPUT_STYLES } from '@/constants/formInputs';

import { CheckboxProps, handleLabelPress } from './services';

const Checkbox = ({
  value,
  onValueChange,
  label,
  customStyle,
}: CheckboxProps) => {
  const handlePress = () => handleLabelPress(onValueChange, value);

  return (
    <View style={[INPUT_STYLES.checkboxContainer, customStyle]}>
      <ExpoCheckbox
        value={value}
        onValueChange={onValueChange}
        color={COLOURS.primary}
        style={INPUT_STYLES.checkbox}
      />
      <TouchableOpacity onPress={handlePress}>
        <TextTypes type='label'>{label}</TextTypes>
      </TouchableOpacity>
    </View>
  );
};

export default Checkbox;
