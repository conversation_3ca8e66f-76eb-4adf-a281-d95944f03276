import React from 'react';
import { TouchableOpacity } from 'react-native';

import TextTypes from '@/components/text-types';
import { COLOURS } from '@/constants/colours';

import type { UserTypeCardProps } from './services';
import { styles } from './styles';

const UserTypeCard: React.FC<UserTypeCardProps> = ({
  icon,
  title,
  description,
  selected = false,
  onPress,
  style,
}) => {
  const {
    card,
    cardSelected,
    title: titleStyle,
    cardDesc: cardDescStyle,
  } = styles;
  return (
    <TouchableOpacity
      style={[card, selected && cardSelected, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      {icon}
      <TextTypes
        type='h2'
        customStyle={titleStyle}
        color={selected ? COLOURS.white : COLOURS.textBlack}
      >
        {title}
      </TextTypes>
      <TextTypes
        type='body2'
        customStyle={cardDescStyle}
        color={selected ? COLOURS.white : COLOURS.youText}
      >
        {description}
      </TextTypes>
    </TouchableOpacity>
  );
};

export default UserTypeCard;
