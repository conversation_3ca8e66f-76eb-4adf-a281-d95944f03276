import { StyleSheet } from 'react-native';
import { COLOURS } from '@/constants/colours';

export const styles = StyleSheet.create({
  card: {
    backgroundColor: COLOURS.white,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: COLOURS.borderColor,
    paddingVertical: 24,
    paddingHorizontal: '12%',
    alignItems: 'center',
    shadowColor: COLOURS.black,
    shadowOpacity: 0.03,
    shadowRadius: 8,
    marginTop: '9%',
    shadowOffset: { width: 0, height: 2 },
  },
  cardSelected: {
    backgroundColor: COLOURS.greenText,
    borderColor: COLOURS.greenText,
  },
  title: {
    marginTop: 6,
    marginBottom: 12,
    textAlign: 'center',
  },
  cardDesc: {
    textAlign: 'center',
    lineHeight: 20,
  },
}); 