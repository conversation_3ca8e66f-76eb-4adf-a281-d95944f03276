import { router } from 'expo-router';
import { useState } from 'react';
import { Alert, Platform, TextInput, View } from 'react-native';

import Button from '@/components/forms/button';
import Checkbox from '@/components/forms/checkbox';
import { ShowEye } from '@/components/icons';
import ErrorIcon from '@/components/icons/Error';
import TextTypes from '@/components/text-types';
import FullScreenLoader from '@/components/ui/FullScreenLoader';
import { COLOURS } from '@/constants/colours';
import { INPUT_STYLES } from '@/constants/formInputs';
import { useAuth } from '@/context/auth';
import { login } from '@/lib/auth/login';

import { styles } from './styles';

const LoginForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isChecked, setChecked] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [emailError, setEmailError] = useState('');
  const { login: loginUser } = useAuth();
  const {
    checkboxContainer,
    inputContainer,
    errorIcon,
    errorContainer,
    detailsContainer,
    paddingv16,
  } = styles;

  const handleLogin = async () => {
    if (!email || !password) {
      setError('Please enter both email and password');
      setEmailError('');
      return;
    }

    // Email validation regex
    const emailRegex = /^[\w-.]+@[\w-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(email)) {
      setEmailError('Please enter a valid email address');
      setError('');
      return;
    }

    setIsLoading(true);
    setError('');
    setEmailError('');

    try {
      await login(email, password);
      await loginUser();
      router.push('/(tabs)');
    } catch (error) {
      console.error('Login failed:', error);
      // Handle login error - show error message to user
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Login failed. Please try again.';
      setError('Your password is incorrect');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View>
      <FullScreenLoader visible={isLoading} />
      <TextTypes customStyle={inputContainer} type='label' color={COLOURS.text}>
        Email
      </TextTypes>
      <TextInput
        placeholder='Email'
        placeholderTextColor={COLOURS.greyDark}
        keyboardType='email-address'
        autoCapitalize='none'
        onChangeText={(text) => {
          setEmail(text);
          setError(''); // Clear error when user starts typing
          setEmailError('');
        }}
        value={email}
        style={INPUT_STYLES.textInput}
        editable={!isLoading}
      />
      {/* Email error message display */}
      {emailError ? (
        <View style={errorContainer}>
          <ErrorIcon style={errorIcon} />
          <TextTypes type='errorText' color={COLOURS.errorText}>
            {emailError}
          </TextTypes>
        </View>
      ) : null}
      <TextTypes customStyle={inputContainer} type='label'>
        Password
      </TextTypes>
      <View
        style={[
          INPUT_STYLES.textInputView,
          Platform.OS == 'android' && paddingv16,
        ]}
      >
        <TextInput
          placeholder='Password'
          placeholderTextColor={COLOURS.greyDark}
          onChangeText={(text) => {
            setPassword(text);
            setError(''); // Clear error when user starts typing
          }}
          value={password}
          secureTextEntry
          editable={!isLoading}
        />
        <ShowEye width={16} height={16} />
      </View>

      {/* Error message display */}
      {error ? (
        <View style={errorContainer}>
          <ErrorIcon style={errorIcon} />
          <TextTypes type='errorText' color={COLOURS.errorText}>
            {error}
          </TextTypes>
        </View>
      ) : null}

      <View style={checkboxContainer}>
        <Checkbox
          value={isChecked}
          onValueChange={setChecked}
          label='Remember me'
        />
        <TextTypes type='link' color={COLOURS.primary}>
          Forgot Password?
        </TextTypes>
      </View>
      <Button
        onPress={handleLogin}
        variant='primary'
        disabled={
          isLoading ||
          !email.trim() ||
          !password.trim() ||
          !!emailError ||
          !!error
        }
      >
        {isLoading ? 'Logging in...' : 'Login'}
      </Button>
      <View style={detailsContainer}>
        <TextTypes type='link' color={COLOURS.text}>
          Don't have an account?
        </TextTypes>
        <TextTypes type='link' color={COLOURS.primary}>
          Sign up
        </TextTypes>
      </View>
    </View>
  );
};

export default LoginForm;
