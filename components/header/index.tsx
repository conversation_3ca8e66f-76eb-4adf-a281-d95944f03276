import { ArrowLeft, Language, Menu1, NewChat } from '../icons';
import TextTypes from '../text-types';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import { TouchableOpacity, View } from 'react-native';

import { COLOURS } from '@/constants/colours';

import { HeaderProps } from './services';
import styles from './styles';

const Header = ({
  noTitle,
  onNewChatPress,
  newChatDisabled,
  onBackPress,
  title = 'VetAssist Pro',
  isBack
}: HeaderProps) => {
  const { container, disabled } = styles;
  const navigation = useNavigation<DrawerNavigationProp<any>>();
  if (noTitle) {
    return (
      <View style={container}>
        <TouchableOpacity onPress={onBackPress}>
          <ArrowLeft color={COLOURS.primary} />
        </TouchableOpacity>
        <Language color={COLOURS.primary} />
      </View>
    );
  }
  return (
    <View style={container}>
      {isBack ? (
        <TouchableOpacity onPress={onBackPress}>
          <ArrowLeft color={COLOURS.primary} />
        </TouchableOpacity>
      ) : (
        <TouchableOpacity onPress={() => navigation.openDrawer()}>
          <Menu1 color={COLOURS.primary} />
        </TouchableOpacity>
      )}
      <TextTypes type='h3' color={COLOURS.primary}>
        {title}
      </TextTypes>
      <TouchableOpacity
        onPress={noTitle ? undefined : onNewChatPress}
        disabled={!!newChatDisabled}
        style={newChatDisabled ? disabled : undefined}
      >
        <NewChat color={COLOURS.primary} />
      </TouchableOpacity>
    </View>
  );
};

export default Header;
