diff --git a/node_modules/expo-modules-autolinking/scripts/android/autolinking_implementation.gradle b/node_modules/expo-modules-autolinking/scripts/android/autolinking_implementation.gradle
index 98c404e..2770252 100644
--- a/node_modules/expo-modules-autolinking/scripts/android/autolinking_implementation.gradle
+++ b/node_modules/expo-modules-autolinking/scripts/android/autolinking_implementation.gradle
@@ -323,7 +323,7 @@ if (rootProject instanceof ProjectDescriptor) {
     }
 
     // Apply plugins for all app projects
-    gradle.afterProject { project ->
+    /* gradle.afterProject { project ->
       if (!project.plugins.hasPlugin('com.android.application')) {
         return
       }
@@ -333,7 +333,7 @@ if (rootProject instanceof ProjectDescriptor) {
           project.plugins.apply(modulePlugin.id)
         }
       }
-    }
+    } */
 
     // Save the manager in the shared context, so that we can later use it in `build.gradle`.
     gradle.ext.expoAutolinkingManager = manager
