import JWT, { SupportedAlgorithms } from 'expo-jwt';
import type { LoginUser, StoredUser, JWTPayload } from '@/types/auth';

export const generateJWT = (payload: JWTPayload): string => {
  const secret = process.env.EXPO_PUBLIC_JWT_SECRET;
  
  if (!secret) {
    throw new Error('JWT secret is not defined in environment variables.');
  }

  const token = JWT.encode(payload, secret, {
    algorithm: SupportedAlgorithms.HS256,
  });

  return token;
};

export const createJWTPayload = (user: LoginUser, expiresIn: number = 24 * 60 * 60): JWTPayload => {
  const now = Math.floor(Date.now() / 1000);
  
  return {
    sub: user.sub.toString(),
    email: user.email,
    isAdmin: user.isAdmin,
    isVet: user.isVet,
    title: user.title,
    firstName: user.firstName,
    lastName: user.lastName,
    iat: now,
    exp: now + expiresIn,
  };
};

export const convertStoredUserToLoginUser = (storedUser: StoredUser): LoginUser => {
  return {
    sub: parseInt(storedUser.id, 10),
    email: storedUser.email,
    isAdmin: false, // Default to false since stored user doesn't have this field
    isVet: storedUser.isVet ? 1 : 0,
    title: storedUser.title || '',
    firstName: storedUser.firstName || '',
    lastName: storedUser.lastName || '',
  };
};

export const generateJWTFromStoredUser = (storedUser: StoredUser, expiresIn: number = 24 * 60 * 60): string => {
  const loginUser = convertStoredUserToLoginUser(storedUser);
  return generateJWTFromLogin(loginUser, expiresIn);
};

export const generateJWTFromLogin = (user: LoginUser, expiresIn: number = 24 * 60 * 60): string => {
  const payload = createJWTPayload(user, expiresIn);
  return generateJWT(payload);
};
