import { getAccessToken, storeToken } from './tokenManager';

export async function refreshAccessToken() {
  try {
    const EQUIVET_URL = process.env.EXPO_PUBLIC_EQUIVET_URL;
    const currentToken = await getAccessToken();

    const response = await fetch(`${EQUIVET_URL}/api/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${currentToken}`,
      },
    });

    if (!response.ok) {
      throw new Error('Refresh failed');
    }

    const data = await response.json();

    const { accessToken } = data;

    await storeToken(accessToken);

    return data;
  } catch (error) {
    console.error('Error during login:', error);
    throw error;
  }
}
