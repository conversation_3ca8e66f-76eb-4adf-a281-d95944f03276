import { storeUser } from '../user/user';

import { storeToken } from './tokenManager';

export async function register(
  firstName: string,
  lastName: string,
  email: string,
  password: string,
  referralCode: string
) {
  try {
    const EQUIVET_URL = process.env.EXPO_PUBLIC_EQUIVET_URL;

    const response = await fetch(
      `${EQUIVET_URL}/api/v1/mobile/register/owner`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName,
          lastName,
          email,
          password,
          referralCode,
        }),
      }
    );

    if (!response.ok) {
      try {
        const errorResponse = await response.json();
        throw new Error(errorResponse.error);
      } catch (error) {
        throw error;
      }
    }

    const data = await response.json();

    const { accessToken, user } = data;
    
    await Promise.all([await storeToken(accessToken), await storeUser(user)]);

    return data;
  } catch (error) {
    console.error('Error during registration:', error);
    throw error;
  }
}
