import AsyncStorage from '@react-native-async-storage/async-storage';
import { JwtPayload, jwtDecode } from 'jwt-decode';

interface ExtendedJwtPayload extends JwtPayload {
  ragToken?: string;
  accessTokenExpires?: string;
}

const ACCESS_TOKEN_KEY = 'accessToken';
const ACCESS_TOKEN_EXPIRES_KEY = 'accessTokenExpires';
const RAG_TOKEN_KEY = 'ragToken';

export const storeToken = async (accessToken: string) => {
  const decodedToken = jwtDecode<ExtendedJwtPayload>(accessToken);

  await AsyncStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
  await AsyncStorage.setItem(
    ACCESS_TOKEN_EXPIRES_KEY,
    decodedToken.accessTokenExpires
      ? decodedToken.accessTokenExpires.toString()
      : ''
  );

  await AsyncStorage.setItem(RAG_TOKEN_KEY, decodedToken.ragToken || '');
};

export const getAccessToken = async () => {
  return await AsyncStorage.getItem(ACCESS_TOKEN_KEY);
};

export const getAccessTokenExpires = async () => {
  return await AsyncStorage.getItem(ACCESS_TOKEN_EXPIRES_KEY);
};

export const getRagToken = async () => {
  return await AsyncStorage.getItem(RAG_TOKEN_KEY);
};

export const clearTokens = async () => {
  await AsyncStorage.removeItem(ACCESS_TOKEN_KEY);
  await AsyncStorage.removeItem(ACCESS_TOKEN_EXPIRES_KEY);
  await AsyncStorage.removeItem(RAG_TOKEN_KEY);
};

export const isUserLoggedIn = async (): Promise<boolean> => {
  try {
    const token = await getAccessToken();
    if (!token) {
      return false;
    }

    // Decode the token to check if it's expired
    const decodedToken = jwtDecode<ExtendedJwtPayload>(token);
    const currentTime = Math.floor(Date.now() / 1000);

    // Check if token is expired
    if (decodedToken.exp && decodedToken.exp < currentTime) {
      // Token is expired, clear it
      await clearTokens();
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error checking login status:', error);
    // If there's an error decoding the token, clear it and return false
    await clearTokens();
    return false;
  }
};
