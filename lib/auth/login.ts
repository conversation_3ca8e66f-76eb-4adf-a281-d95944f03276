import { storeUser } from '../user/user';

import { storeToken } from './tokenManager';

export async function login(email: string, password: string) {
  try {
    const EQUIVET_URL = process.env.EXPO_PUBLIC_EQUIVET_URL;

    const response = await fetch(`${EQUIVET_URL}/api/v1/mobile/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    if (!response.ok) {
      throw new Error('Login failed');
    }

    const data = await response.json();

    const { accessToken, user } = data;

    await Promise.all([await storeToken(accessToken), await storeUser(user)]);

    return data;
  } catch (error) {
    console.error('Error during login:', error);
    throw error;
  }
}
