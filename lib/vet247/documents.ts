import { getAccessToken } from '@/lib/auth/tokenManager';

export async function getS3SignedUrl(
  fileName: string,
  fileType: string,
  accessToken: string
): Promise<string> {
  if (!accessToken) throw new Error('No authentication token found');
  const params = new URLSearchParams({
    fileName,
    fileType,
    requestMethod: 'PUT',
  });
  const EQUIVET_URL = process.env.EXPO_PUBLIC_EQUIVET_URL;
  const response = await fetch(
    `${EQUIVET_URL}/api/v1/get-s3-file-url?${params.toString()}`,
    {
      method: 'GET',
      headers: {
        accept: 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
    }
  );
  if (!response.ok) throw new Error('Failed to get signed URL');
  const data = await response.json();
  if (!data.url) throw new Error('No signed URL returned from API');
  return data.url;
}

export async function getS3SignedImageUrl(fileName: string): Promise<string> {
  const accessToken = await getAccessToken();
  if (!accessToken) throw new Error('No authentication token found');
  const params = new URLSearchParams({
    fileName,
    requestMethod: 'GET',
  });
  const EQUIVET_URL = process.env.EXPO_PUBLIC_EQUIVET_URL;
  const response = await fetch(
    `${EQUIVET_URL}/api/v1/get-s3-file-url?${params.toString()}`,
    {
      method: 'GET',
      headers: {
        accept: 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
    }
  );
  if (!response.ok) throw new Error('Failed to get signed URL');
  const data = await response.json();
  if (!data.url) throw new Error('No signed URL returned from API');
  return data.url;
} 