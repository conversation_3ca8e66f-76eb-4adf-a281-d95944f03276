import { refreshAccessToken } from '../auth/refresh';
import { getAccessToken, getAccessTokenExpires } from '../auth/tokenManager';

export const makeVetApiCall = async (
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  body?: any,
  headers: Record<string, string> = {}
): Promise<any> => {
  try {
    const EQUIVET_URL = process.env.EXPO_PUBLIC_EQUIVET_URL;
    const accessTokenExpires = await getAccessTokenExpires();

    if (
      accessTokenExpires &&
      Date.now() > new Date(accessTokenExpires!).getTime()
    ) {
      await refreshAccessToken();
    }

    const accessToken = await getAccessToken();

    const response = await fetch(`${EQUIVET_URL}/api/v1/${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
        ...headers,
      },
      body: body ? JSON.stringify(body) : undefined,
    });

    if (!response.ok) {
      throw new Error(
        `API call to ${endpoint} failed with status ${response.status}`
      );
    }

    return await response.json();
  } catch (error) {
    console.error(`Error making API call to ${endpoint}:`, error);
    throw error;
  }
};
