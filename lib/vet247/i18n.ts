import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import enUK from '@locales/en-uk/translation.json';
import enUS from '@locales/en-us/translation.json';
import ar from '@locales/ar/translation.json';
import de from '@locales/de/translation.json';
import es from '@locales/es/translation.json';
import it from '@locales/it/translation.json';
import pt from '@locales/pt/translation.json';
import th from '@locales/th/translation.json';

i18n
  .use(initReactI18next)
  .init({
    lng: 'en-GB',
    fallbackLng: 'en-GB',
    resources: {
      'en-GB': { translation: enUK },
      'en-US': { translation: enUS },
      ar: { translation: ar },
      de: { translation: de },
      es: { translation: es },
      it: { translation: it },
      pt: { translation: pt },
      th: { translation: th },
    },
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: false
    }
  });

export default i18n; 