import { getRagToken } from '../auth/tokenManager';

// import { generateJWTFromStoredUser } from '@/lib/auth/jwtGenerator';
import type { StoredUser } from '@/types/auth';

export interface GenerateAnswerResponse {
  answer: string;
  [key: string]: any;
}

export async function generateAnswer(
  user: StoredUser,
  question: string,
  chat_summary: string = '',
  recent_messages: any[] = []
): Promise<GenerateAnswerResponse> {
  try {
    if (!user) throw new Error('User not found');
    const token = await getRagToken();

    const RAG_URL = process.env.EXPO_PUBLIC_RAG_URL;
    const response = await fetch(`${RAG_URL}/api/generate-answer`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        userId: user.id + '',
        question,
        chat_summary,
        recent_messages,
      }),
    });
    if (!response.ok) {
      throw new Error('Failed to get AI response');
    }
    return await response.json();
  } catch (error) {
    console.error('Error generating AI answer:', error);
    throw error;
  }
} 