// @ts-ignore
import * as SQLite from 'expo-sqlite';

const db = SQLite.openDatabaseSync('vetassist_chat.db');

export interface Conversation {
  id: number;
  created_at: string;
  title: string;
}

export interface Message {
  id: number;
  conversation_id: number;
  sender: 'user' | 'ai';
  text: string;
  timestamp: string;
}

export function initDB() {
  db.execSync(`
    CREATE TABLE IF NOT EXISTS conversations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      created_at TEXT DEFAULT CURRENT_TIMESTAMP,
      title TEXT
    );
    CREATE TABLE IF NOT EXISTS messages (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      conversation_id INTEGER,
      sender TEXT,
      text TEXT,
      timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY(conversation_id) REFERENCES conversations(id)
    );
  `);
}

export function createConversation(title: string = ''): number {
  const result = db.runSync(
    'INSERT INTO conversations (title) VALUES (?);',
    [title]
  );
  return result.lastInsertRowId;
}

export function addMessage(conversation_id: number, sender: string, text: string): number {
  const result = db.runSync(
    'INSERT INTO messages (conversation_id, sender, text) VALUES (?, ?, ?);',
    [conversation_id, sender, text]
  );
  return result.lastInsertRowId;
}

export function getConversations(): Conversation[] {
  const result = db.getAllSync('SELECT * FROM conversations ORDER BY created_at DESC;');
  return result as Conversation[];
}

export function getMessagesForConversation(conversation_id: number): Message[] {
  const result = db.getAllSync(
    'SELECT * FROM messages WHERE conversation_id = ? ORDER BY timestamp ASC;',
    [conversation_id]
  );
  return result as Message[];
}

export function deleteConversation(conversation_id: number): void {
  db.runSync('DELETE FROM messages WHERE conversation_id = ?;', [conversation_id]);
  db.runSync('DELETE FROM conversations WHERE id = ?;', [conversation_id]);
} 