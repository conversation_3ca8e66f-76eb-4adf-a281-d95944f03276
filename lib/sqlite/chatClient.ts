import {
  initDB,
  getConversations,
  getMessagesForConversation,
  createConversation,
  addMessage,
} from './chatStorage';

class ChatClient {
  private initialized = false;

  ensureInit() {
    if (!this.initialized) {
      initDB();
      this.initialized = true;
    }
  }

  getConversations() {
    this.ensureInit();
    return getConversations();
  }

  getMessagesForConversation(conversationId: number) {
    this.ensureInit();
    return getMessagesForConversation(conversationId);
  }

  createConversation(title: string) {
    this.ensureInit();
    return createConversation(title);
  }

  addMessage(conversationId: number, sender: string, text: string) {
    this.ensureInit();
    return addMessage(conversationId, sender, text);
  }

  getOrCreateFirstConversationWithMessages() {
    this.ensureInit();
    let convs = getConversations();
    let conversationId: number;
    let messages: any[] = [];
    if (convs.length > 0) {
      conversationId = convs[0].id;
      messages = getMessagesForConversation(conversationId);
    } else {
      conversationId = createConversation('');
      messages = [];
    }
    return { conversationId, messages };
  }
}

export default new ChatClient(); 