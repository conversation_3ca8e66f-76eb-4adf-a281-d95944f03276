import AsyncStorage from '@react-native-async-storage/async-storage';
import type { StoredUser } from '@/types/auth';

const USER_ID_KEY = 'userId';
const USER_IS_VET = 'userIsVet';
const USER_EMAIL_KEY = 'userEmail';
const USER_TITLE_KEY = 'userTitle';
const USER_FIRST_NAME_KEY = 'userFirstName';
const USER_LAST_NAME_KEY = 'userLastName';

export const storeUser = async (user: StoredUser) => {
  await AsyncStorage.setItem(USER_ID_KEY, user.sub.toString());
  await AsyncStorage.setItem(USER_IS_VET, user.isVet.toString());
  await AsyncStorage.setItem(USER_EMAIL_KEY, user.email);
  await AsyncStorage.setItem(USER_TITLE_KEY, user.title ? user.title : '');
  await AsyncStorage.setItem(USER_FIRST_NAME_KEY, user.firstName);
  await AsyncStorage.setItem(USER_LAST_NAME_KEY, user.lastName);
};

export const getUserId = async () => {
  return await AsyncStorage.getItem(USER_ID_KEY);
};

export const getUserIsVet = async () => {
  return await AsyncStorage.getItem(USER_IS_VET);
};

export const getUserEmail = async () => {
  return await AsyncStorage.getItem(USER_EMAIL_KEY);
};

export const getUserTitle = async () => {
  return await AsyncStorage.getItem(USER_TITLE_KEY);
};

export const getUserName = async () => {
  const firstName = await AsyncStorage.getItem(USER_FIRST_NAME_KEY);
  const lastName = await AsyncStorage.getItem(USER_LAST_NAME_KEY);
  return `${firstName || ''} ${lastName || ''}`.trim();
};

export const getUserFirstName = async () => {
  return await AsyncStorage.getItem(USER_FIRST_NAME_KEY);
};

export const getUserLastName = async () => {
  return await AsyncStorage.getItem(USER_LAST_NAME_KEY);
};

export const getUserObject = async () => {
  const [userId, userEmail, userIsVet, userTitle, userFirstName, userLastName] =
    await Promise.all([
      getUserId(),
      getUserEmail(),
      getUserIsVet(),
      getUserTitle(),
      getUserFirstName(),
      getUserLastName(),
    ]);

  return {
    id: userId!,
    email: userEmail!,
    isVet: userIsVet === 'true',
    title: userTitle || undefined,
    firstName: userFirstName || undefined,
    lastName: userLastName || undefined,
  };
};

export const clearUser = async () => {
  await AsyncStorage.removeItem(USER_ID_KEY);
  await AsyncStorage.removeItem(USER_IS_VET);
  await AsyncStorage.removeItem(USER_EMAIL_KEY);
  await AsyncStorage.removeItem(USER_TITLE_KEY);
  await AsyncStorage.removeItem(USER_FIRST_NAME_KEY);
  await AsyncStorage.removeItem(USER_LAST_NAME_KEY);
};
