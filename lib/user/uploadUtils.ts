/**
 * Upload a file to S3 using a signed URL.
 * @param signedUrl The signed S3 URL for uploading
 * @param fileUri The local file URI
 * @param fileType The MIME type of the file (e.g., 'image/jpeg')
 * @throws Error if the upload fails
 */
export async function uploadToS3(signedUrl: string, fileUri: string, fileType: string) {
  const response = await fetch(fileUri);
  const blob = await response.blob();
  const upload = await fetch(signedUrl, {
    method: 'PUT',
    headers: { 'Content-Type': fileType },
    body: blob,
  });
  if (!upload.ok) throw new Error('Failed to upload image to S3');
} 