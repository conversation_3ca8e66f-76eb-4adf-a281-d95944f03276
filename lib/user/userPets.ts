import type { AddPetInput } from '@/types/pet';

export async function getUserPets(userId: string, accessToken: string) {
  try {
    const EQUIVET_URL = process.env.EXPO_PUBLIC_EQUIVET_URL;
    const response = await fetch(`${EQUIVET_URL}/api/v1/user/${userId}/pets`, {
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
    });
    if (!response.ok) {
      throw new Error('Failed to fetch pets');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching user pets:', error);
    throw error;
  }
}

export async function addPet(
  userId: string,
  accessToken: string,
  pet: AddPetInput
) {
  try {
    const EQUIVET_URL = process.env.EXPO_PUBLIC_EQUIVET_URL;
    const response = await fetch(`${EQUIVET_URL}/api/v1/user/${userId}/pets`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        name: pet.name,
        species: pet.species,
        breed: pet.breed,
        gender: pet.sex,
        weight:
          typeof pet.weight === 'string' ? parseFloat(pet.weight) : pet.weight,
        weightType: pet.weightUnit,
        dateOfBirth: pet.dob ? pet.dob.toISOString().split('T')[0] : null,
        photo: pet.photo || undefined,
        microchipNumber: pet.microchipNumber,
        insuranceProvider: pet.insuranceProvider,
        insurancePolicyNumber: pet.insurancePolicyNumber,
        vetPracticeId: pet.vetPracticeId,
        addedByReferral: pet.addedByReferral,
        petPicture: pet.photo
      }),
    });
    if (!response.ok) {
      try {
        const errorResponse = await response.json();
        console.log(errorResponse);

        throw new Error(errorResponse.error);
      } catch (error) {
        throw error;
      }
    }
    return await response.json();
  } catch (error) {
    console.error('Error adding pet:', error);
    throw error;
  }
}
