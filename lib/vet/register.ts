import { storeToken } from '../auth/tokenManager';
import { storeUser } from '../user/user';

export async function registerVetProfessional(
  firstName: string,
  lastName: string,
  email: string,
  password: string,
  professionalRole: string,
  practiceId: number
) {
  try {
    const EQUIVET_URL = process.env.EXPO_PUBLIC_EQUIVET_URL;

    const response = await fetch(`${EQUIVET_URL}/api/v1/mobile/register/vet`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        firstName,
        lastName,
        email,
        password,
        professionalRole,
        practiceId,
      }),
    });

    if (!response.ok) {
      try {
        const errorResponse = await response.json();
        throw new Error(
          errorResponse.error || errorResponse.message || 'Registration failed'
        );
      } catch (error) {
        throw error;
      }
    }

    const data = await response.json();
    const { accessToken, user } = data;
    await Promise.all([await storeToken(accessToken), await storeUser(user)]);
    return data;
  } catch (error) {
    console.error('Error during vet professional registration:', error);
    throw error;
  }
}
