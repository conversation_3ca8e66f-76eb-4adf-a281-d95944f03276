import { getUserId } from '@/lib/user/user';
import { getAccessToken } from '@/lib/auth/tokenManager';

export interface UpdatePetRequest {
  name: string;
  species: string;
  breed: string;
  gender: string;
  weight: number;
  weightType: string;
  dateOfBirth: string | null;
  microchipNumber?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  vetPracticeId?: number;
  addedByReferral?: boolean;
  petPicture?: string;
}

export async function updatePet(petId: string | number, petData: UpdatePetRequest) {
  try {
    const userId = await getUserId();
    if (!userId) throw new Error('User ID not found');
    
    const accessToken = await getAccessToken();
    if (!accessToken) throw new Error('Access token not found');
    
    const EQUIVET_URL = process.env.EXPO_PUBLIC_EQUIVET_URL;
    
    const response = await fetch(`${EQUIVET_URL}/api/v1/user/${userId}/pets/${petId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify(petData),
    });
    
    if (!response.ok) {
      const errorResponse = await response.json();
      throw new Error(errorResponse.message || 'Failed to update pet');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error updating pet:', error);
    throw error;
  }
}
