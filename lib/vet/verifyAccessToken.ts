export async function verifyAccessToken(code: string) {
  try {
    const EQUIVET_URL = process.env.EXPO_PUBLIC_EQUIVET_URL;
    const response = await fetch(`${EQUIVET_URL}/api/v1/practice/access-code/${encodeURIComponent(code)}`, {
      method: 'GET',
      headers: {
        accept: 'application/json',
      },
    });

    if (!response.ok) {
      let errorMsg = 'Invalid code';
      try {
        const errorData = await response.json();
        errorMsg = errorData?.message || errorMsg;
      } catch {}
      throw new Error(errorMsg);
    }

    return await response.json();
  } catch (error: any) {
    // Optionally log error
    throw new Error(error.message || 'Unknown error');
  }
} 