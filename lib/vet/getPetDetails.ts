import { getUserId } from '@/lib/user/user';
import { getAccessToken } from '@/lib/auth/tokenManager';

export async function getPetDetails(petId: string | number) {
  try {
    const userId = await getUserId();
    if (!userId) throw new Error('User ID not found');
    const accessToken = await getAccessToken();
    if (!accessToken) throw new Error('Access token not found');
    const EQUIVET_URL = process.env.EXPO_PUBLIC_EQUIVET_URL;
    const response = await fetch(`${EQUIVET_URL}/api/v1/user/${userId}/pets/${petId}`, {
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
    });
    if (!response.ok) {
      throw new Error('Failed to fetch pet details');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching pet details:', error);
    throw error;
  }
}
