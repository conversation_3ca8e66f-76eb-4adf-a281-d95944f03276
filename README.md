# VetAssist - Veterinary Assistant App

This is an [Expo](https://expo.dev) project created with [`create-expo-app`](https://www.npmjs.com/package/create-expo-app) for veterinary assistance and pet care management.

## Table of Contents

- [Get Started](#get-started)
- [Project Structure](#project-structure)
  - [Components](#components)
- [Component Documentation](#component-documentation)
  - [Icons Component System](#icons-component-system)
    - [Structure](#structure)
    - [How to Use Icons](#how-to-use-icons)
    - [Icon Component Props](#icon-component-props)
    - [Available Icons](#available-icons)
  - [Form Components](#form-components)
    - [Button Component](#button-component)
    - [Checkbox Component](#checkbox-component)
    - [Login Form Component](#login-form-component)
  - [Typography Components](#typography-components)
    - [ThemedText Component](#themedtext-component)
    - [TextTypes Component](#texttypes-component)
  - [Header Components](#header-components)
    - [Header Component](#header-component)
  - [Section Title Component](#section-title-component)
  - [Default Expo Components](#default-expo-components)
- [Development Guidelines](#development-guidelines)
  - [Component Naming](#component-naming)
  - [Component Structure](#component-structure)
  - [Styling](#styling)
  - [Testing](#testing)
- [Get a Fresh Project](#get-a-fresh-project)
- [Learn More](#learn-more)
- [Join the Community](#join-the-community)

## Get started

1. Copy env file and populate the variables with applicable values

   ```bash
   cp .env.example .env
   ```

2. Install dependencies

   ```bash
   npm install
   ```

3. Start the app

   ```bash
    npx expo start
   ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Project Structure

### Components

The project uses a modular component architecture with the following structure:

```
components/
├── icons/           # SVG icon components
├── forms/           # Form-related components
├── text-types/      # Typography components
├── section-title/   # Section header components
├── header/          # Header components
├── ui/              # Default Expo UI utility components (left for reference)
└── [other components]
```

## Component Documentation

### Icons Component System

The icons component system is a comprehensive SVG-based icon library built using `react-native-svg`. Here's how it works:

#### Structure

- **Location**: `components/icons/`
- **Main file**: `components/icons/index.js` - exports all icons
- **Individual icons**: Each icon is a separate `.js` file (e.g., `Add.js`, `Home.js`)

#### How to Use Icons

```jsx
import { Add, Home, Search } from '@/components/icons';

// Basic usage with default props
<Add />

// Custom size and color
<Home width={32} height={32} color="#007AFF" />

// With additional props
<Search width={24} height={24} color="#FF0000" style={{ marginRight: 8 }} />
```

#### Icon Component Props

All icon components accept these props:

- `width` (number, default: 24) - Icon width in pixels
- `height` (number, default: 24) - Icon height in pixels
- `color` (string, default: '#160F29') - Icon color
- `...props` - Any additional props passed to the underlying SVG component

#### Available Icons

The icon library includes 100+ icons covering:

- **Navigation**: Home, ArrowLeft, ArrowRight, ChevronUp, etc.
- **Actions**: Add, Edit, Delete, Save, Search, etc.
- **Animals**: Dog, Cat, PawPrint, etc.
- **Medical**: Stethoscope, Syringe, Vaccination, etc.
- **UI Elements**: Close, Check, Alert, Info, etc.
- **Communication**: Mail, Phone, Whatsapp, etc.
- **And many more...**

Please refer to the supplied reference Figma file for the icons and their usage.

### Form Components

#### Button Component

Located in `components/forms/button/`

```jsx
import Button from '@/components/forms/button';

// Available variants: primary, secondary, outline, tertiary, disabled, small, smallSecondary, smallOutline, smallTertiary
<Button variant="primary" onPress={handlePress}>
  Click Me
</Button>

<Button variant="outline" customStyle={{ marginTop: 16 }}>
  Secondary Action
</Button>
```

**Props:**

- `variant` - Button style variant
- `children` - Button text (string)
- `customStyle` - Additional styles (prefer using styles.ts file for component-specific styles)
- `textColor` - Custom text color
- `disabled` - Disable button
- All standard TouchableOpacity props

**Note:** The preferred way to add custom styles is by creating a class in the `styles.ts` file for the component you're working with, rather than using inline `customStyle` props. Please refer to the [Styling Guidelines](#styling) section for more details.

#### Checkbox Component

Located in `components/forms/checkbox/`

```jsx
import Checkbox from '@/components/forms/checkbox';

<Checkbox
  value={isChecked}
  onValueChange={setIsChecked}
  label='Accept terms and conditions'
/>;
```

#### Login Form Component

Located in `components/forms/login-form/`

```jsx
import LoginForm from '@/components/forms/login-form';

<LoginForm />;
```

A complete login form component that includes email input, password input with show/hide functionality, remember me checkbox, and login button.

### Typography Components

#### TextTypes Component

Located in `components/text-types/`

```jsx
import TextTypes from '@/components/text-types';

<TextTypes type='buttonText' color='#007AFF'>
  Button Text
</TextTypes>;
```

### Header Components

#### Header Component

Located in `components/header/`

```jsx
import Header from '@/components/header';

<Header title='Screen Title' showBackButton={true} onBackPress={handleBack} />;
```

### Section Title Component

Located in `components/section-title/`

```jsx
import SectionTitle from '@/components/section-title';

<SectionTitle title='Section Name' subtitle='Optional subtitle' />;
```

### Default Expo Components

The following components are default Expo files left for reference and have no specific instructions:

#### UI Directory Components

- `components/ui/IconSymbol.ios.tsx`
- `components/ui/IconSymbol.tsx`
- `components/ui/TabBarBackground.ios.tsx`
- `components/ui/TabBarBackground.tsx`

#### Single File Components

- `components/Collapsible.tsx`
- `components/ExternalLink.tsx`
- `components/HapticTab.tsx`
- `components/ParallaxScrollView.tsx`
- `components/ThemedView.tsx`
- `components/ThemedText.tsx`

These components are part of the default Expo template and are provided as reference implementations. They can be used as-is or modified according to your project needs.

## Development Guidelines

### Component Naming

- Use PascalCase for component names
- Use camelCase for file names
- Group related components in subdirectories
- All components follow the structure of:

### Component Structure

- All components follow the structure of:

```
component-title/
├── index.tsx
├── styles.ts
└── services.ts
```

With services.ts file for any component-specific logic such as event handlers, API calls, functions, etc and also for types/interfaces and such.

### Styling

- Use StyleSheet for component styles
- **Preferred approach**: Create styles in the `styles.ts` file for each component rather than using inline `customStyle` props
- Leverage the theme system for colors
- Follow the existing design patterns

**Example of preferred styling approach:**

```jsx
// In your component file
import { styles } from './styles';

const { customButton } = styles;

<Button variant="primary" style={customButton}>
  Custom Styled Button
</Button>

// In styles.ts
export const styles = StyleSheet.create({
  customButton: {
    marginTop: 16,
    borderRadius: 8,
    // other custom styles
  },
});
```

### Testing

- Components have tests in `components/__tests__/`
- Run tests with `npm test`

## Get a fresh project

When you're ready, run:

```bash
npm run reset-project
```

This command will move the starter code to the **app-example** directory and create a blank **app** directory where you can start developing.

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.
