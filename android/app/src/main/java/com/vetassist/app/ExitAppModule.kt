package com.vetassist.app

import android.os.Build
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import kotlin.system.exitProcess

class ExitAppModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    override fun getName(): String {
        return "ExitAppModule"
    }

    @ReactMethod
    fun exitApp() {
        val activity = currentActivity
        if (activity != null) {
            activity.finishAffinity() // Close all activities
            exitProcess(0)   // Kill the app process
        }
    }
} 